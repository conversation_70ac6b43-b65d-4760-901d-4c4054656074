"""
图像处理模块
提供图像预处理功能，提高条形码识别准确率
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import logging
from typing import Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageProcessor:
    """图像处理器类"""
    
    def __init__(self):
        """初始化图像处理器"""
        logger.info("图像处理器初始化完成")
    
    def preprocess_for_barcode(self, image: np.ndarray) -> np.ndarray:
        """
        为条形码识别预处理图像

        Args:
            image: 输入图像

        Returns:
            预处理后的图像
        """
        try:
            # 检查图像是否有效
            if image is None or image.size == 0:
                logger.error("传入的图像为空")
                return image

            # 检查图像维度
            if len(image.shape) < 2:
                logger.error("图像维度不正确")
                return image

            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # 应用自适应阈值
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )

            # 形态学操作
            kernel = np.ones((3, 3), np.uint8)
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

            return processed

        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            return image
    
    def enhance_contrast(self, image: np.ndarray, factor: float = 1.5) -> np.ndarray:
        """
        增强图像对比度
        
        Args:
            image: 输入图像
            factor: 对比度增强因子
            
        Returns:
            对比度增强后的图像
        """
        try:
            # 转换为PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(pil_image)
            enhanced = enhancer.enhance(factor)
            
            # 转换回OpenCV格式
            result = cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)
            
            return result
            
        except Exception as e:
            logger.error(f"对比度增强失败: {str(e)}")
            return image
    
    def enhance_brightness(self, image: np.ndarray, factor: float = 1.2) -> np.ndarray:
        """
        增强图像亮度
        
        Args:
            image: 输入图像
            factor: 亮度增强因子
            
        Returns:
            亮度增强后的图像
        """
        try:
            # 转换为PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # 增强亮度
            enhancer = ImageEnhance.Brightness(pil_image)
            enhanced = enhancer.enhance(factor)
            
            # 转换回OpenCV格式
            result = cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)
            
            return result
            
        except Exception as e:
            logger.error(f"亮度增强失败: {str(e)}")
            return image
    
    def sharpen_image(self, image: np.ndarray) -> np.ndarray:
        """
        锐化图像
        
        Args:
            image: 输入图像
            
        Returns:
            锐化后的图像
        """
        try:
            # 定义锐化核
            kernel = np.array([[-1, -1, -1],
                             [-1,  9, -1],
                             [-1, -1, -1]])
            
            # 应用锐化
            sharpened = cv2.filter2D(image, -1, kernel)
            
            return sharpened
            
        except Exception as e:
            logger.error(f"图像锐化失败: {str(e)}")
            return image
    
    def remove_noise(self, image: np.ndarray) -> np.ndarray:
        """
        去除图像噪声
        
        Args:
            image: 输入图像
            
        Returns:
            去噪后的图像
        """
        try:
            # 使用双边滤波去噪
            denoised = cv2.bilateralFilter(image, 9, 75, 75)
            
            return denoised
            
        except Exception as e:
            logger.error(f"图像去噪失败: {str(e)}")
            return image
    
    def rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """
        旋转图像
        
        Args:
            image: 输入图像
            angle: 旋转角度（度）
            
        Returns:
            旋转后的图像
        """
        try:
            height, width = image.shape[:2]
            center = (width // 2, height // 2)
            
            # 计算旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # 应用旋转
            rotated = cv2.warpAffine(image, rotation_matrix, (width, height))
            
            return rotated
            
        except Exception as e:
            logger.error(f"图像旋转失败: {str(e)}")
            return image
    
    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            target_size: 目标大小 (width, height)
            
        Returns:
            调整大小后的图像
        """
        try:
            resized = cv2.resize(image, target_size, interpolation=cv2.INTER_AREA)
            return resized
            
        except Exception as e:
            logger.error(f"图像大小调整失败: {str(e)}")
            return image
    
    def crop_roi(self, image: np.ndarray, roi: Tuple[int, int, int, int]) -> np.ndarray:
        """
        裁剪感兴趣区域
        
        Args:
            image: 输入图像
            roi: 感兴趣区域 (x, y, width, height)
            
        Returns:
            裁剪后的图像
        """
        try:
            x, y, w, h = roi
            cropped = image[y:y+h, x:x+w]
            return cropped
            
        except Exception as e:
            logger.error(f"图像裁剪失败: {str(e)}")
            return image
    
    def auto_enhance_for_barcode(self, image: np.ndarray) -> np.ndarray:
        """
        自动增强图像以提高条形码识别率

        Args:
            image: 输入图像

        Returns:
            自动增强后的图像
        """
        try:
            logger.info("🔧 开始自动图像增强处理...")

            # 步骤1: 去噪
            logger.info("步骤1: 应用去噪处理...")
            denoised = self.remove_noise(image)
            logger.info("去噪处理完成")

            # 步骤2: 增强对比度
            logger.info("步骤2: 增强对比度 (因子: 1.3)...")
            contrast_enhanced = self.enhance_contrast(denoised, 1.3)
            logger.info("对比度增强完成")

            # 步骤3: 轻微锐化
            logger.info("步骤3: 应用图像锐化...")
            sharpened = self.sharpen_image(contrast_enhanced)
            logger.info("图像锐化完成")

            # 步骤4: 条形码专用预处理
            logger.info("步骤4: 条形码专用预处理...")
            final = self.preprocess_for_barcode(sharpened)
            logger.info("条形码预处理完成")

            logger.info("✅ 自动图像增强处理完成")
            return final

        except Exception as e:
            logger.error(f"自动图像增强失败: {str(e)}")
            return image
    
    def detect_barcode_region(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        检测图像中可能的条形码区域
        
        Args:
            image: 输入图像
            
        Returns:
            条形码区域坐标 (x, y, width, height) 或 None
        """
        try:
            # 转换为灰度图像
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算梯度
            grad_x = cv2.Sobel(gray, cv2.CV_32F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_32F, 0, 1, ksize=3)
            
            # 计算梯度幅度
            gradient = cv2.subtract(grad_x, grad_y)
            gradient = cv2.convertScaleAbs(gradient)
            
            # 模糊和阈值处理
            blurred = cv2.blur(gradient, (9, 9))
            _, thresh = cv2.threshold(blurred, 225, 255, cv2.THRESH_BINARY)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (21, 7))
            closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # 腐蚀和膨胀
            closed = cv2.erode(closed, None, iterations=4)
            closed = cv2.dilate(closed, None, iterations=4)
            
            # 查找轮廓
            contours, _ = cv2.findContours(closed.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 找到最大的轮廓
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)
                
                # 验证区域大小
                if w > 50 and h > 20:
                    return (x, y, w, h)
            
            return None
            
        except Exception as e:
            logger.error(f"条形码区域检测失败: {str(e)}")
            return None


# 测试函数
def test_image_processor():
    """测试图像处理器功能"""
    processor = ImageProcessor()
    
    # 创建测试图像
    test_image = np.zeros((200, 400, 3), dtype=np.uint8)
    test_image.fill(128)  # 灰色背景
    
    print("测试图像处理功能...")
    
    # 测试各种处理方法
    enhanced = processor.auto_enhance_for_barcode(test_image)
    print(f"自动增强完成，输出图像形状: {enhanced.shape}")
    
    region = processor.detect_barcode_region(test_image)
    print(f"条形码区域检测结果: {region}")
    
    print("测试完成")


if __name__ == "__main__":
    test_image_processor()
