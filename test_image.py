#!/usr/bin/env python3
"""
测试特定图片的二维码识别
"""

import sys
import os
import cv2
import numpy as np

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner
from modules.image_processor import ImageProcessor


def test_image_recognition(image_path):
    """测试图片识别"""
    print(f"测试图片: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    scanner = BarcodeScanner()
    processor = ImageProcessor()
    
    try:
        # 读取原始图片
        print("1. 读取原始图片...")
        image = cv2.imread(image_path)
        if image is None:
            print("❌ 无法读取图片文件")
            return
        
        print(f"   图片尺寸: {image.shape}")
        
        # 尝试直接识别
        print("2. 尝试直接识别...")
        results = scanner._decode_barcodes(image)
        if results:
            print(f"✅ 直接识别成功，找到 {len(results)} 个码:")
            for i, result in enumerate(results, 1):
                print(f"   码 {i}: {result['data']} (类型: {result['type']})")
            return
        else:
            print("   直接识别失败，尝试图像增强...")
        
        # 尝试各种图像增强方法
        enhancement_methods = [
            ("对比度增强", lambda img: processor.enhance_contrast(img, 1.5)),
            ("亮度增强", lambda img: processor.enhance_brightness(img, 1.3)),
            ("自动增强", lambda img: processor.auto_enhance_for_barcode(img)),
            ("去噪处理", lambda img: processor.remove_noise(img)),
            ("锐化处理", lambda img: processor.sharpen_image(img)),
        ]
        
        for method_name, enhance_func in enhancement_methods:
            print(f"3. 尝试{method_name}...")
            try:
                enhanced_image = enhance_func(image)
                
                # 如果是灰度图像，转换为BGR用于识别
                if len(enhanced_image.shape) == 2:
                    enhanced_image = cv2.cvtColor(enhanced_image, cv2.COLOR_GRAY2BGR)
                
                results = scanner._decode_barcodes(enhanced_image)
                if results:
                    print(f"✅ {method_name}识别成功，找到 {len(results)} 个码:")
                    for i, result in enumerate(results, 1):
                        print(f"   码 {i}: {result['data']} (类型: {result['type']})")
                    return
                else:
                    print(f"   {method_name}识别失败")
                    
            except Exception as e:
                print(f"   {method_name}处理出错: {e}")
        
        # 尝试不同的图像预处理组合
        print("4. 尝试组合增强...")
        try:
            # 组合处理：去噪 + 对比度增强 + 锐化
            step1 = processor.remove_noise(image)
            step2 = processor.enhance_contrast(step1, 1.8)
            step3 = processor.sharpen_image(step2)
            
            results = scanner._decode_barcodes(step3)
            if results:
                print(f"✅ 组合增强识别成功，找到 {len(results)} 个码:")
                for i, result in enumerate(results, 1):
                    print(f"   码 {i}: {result['data']} (类型: {result['type']})")
                return
            else:
                print("   组合增强识别失败")
                
        except Exception as e:
            print(f"   组合增强处理出错: {e}")
        
        # 尝试调整图片大小
        print("5. 尝试调整图片大小...")
        try:
            height, width = image.shape[:2]
            
            # 尝试不同的缩放比例
            scales = [0.5, 1.5, 2.0, 0.8, 1.2]
            for scale in scales:
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                enhanced = processor.auto_enhance_for_barcode(resized)
                
                if len(enhanced.shape) == 2:
                    enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
                
                results = scanner._decode_barcodes(enhanced)
                if results:
                    print(f"✅ 缩放{scale}x识别成功，找到 {len(results)} 个码:")
                    for i, result in enumerate(results, 1):
                        print(f"   码 {i}: {result['data']} (类型: {result['type']})")
                    return
                    
        except Exception as e:
            print(f"   缩放处理出错: {e}")
        
        # 尝试旋转图片
        print("6. 尝试旋转图片...")
        try:
            angles = [90, 180, 270, 45, -45]
            for angle in angles:
                rotated = processor.rotate_image(image, angle)
                enhanced = processor.auto_enhance_for_barcode(rotated)
                
                if len(enhanced.shape) == 2:
                    enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
                
                results = scanner._decode_barcodes(enhanced)
                if results:
                    print(f"✅ 旋转{angle}度识别成功，找到 {len(results)} 个码:")
                    for i, result in enumerate(results, 1):
                        print(f"   码 {i}: {result['data']} (类型: {result['type']})")
                    return
                    
        except Exception as e:
            print(f"   旋转处理出错: {e}")
        
        print("❌ 尝试了所有方法都无法识别此图片中的二维码/条形码")
        print("\n可能的原因:")
        print("  - 图片质量不够清晰")
        print("  - 二维码/条形码损坏或模糊")
        print("  - 光照条件不佳")
        print("  - 图片角度不正确")
        print("  - 二维码/条形码格式不支持")
        
        print("\n建议:")
        print("  - 确保图片清晰度足够")
        print("  - 调整光照条件重新拍摄")
        print("  - 确保二维码/条形码完整可见")
        print("  - 尝试不同角度拍摄")
        
    except Exception as e:
        print(f"❌ 处理图片时发生错误: {e}")


def main():
    """主函数"""
    image_path = "A44D407E-7666-4D26-90F1-D67E70FDEC4B-60305-000011.jpg"
    
    print("=" * 60)
    print("二维码/条形码识别测试")
    print("=" * 60)
    
    test_image_recognition(image_path)


if __name__ == "__main__":
    main()
