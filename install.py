#!/usr/bin/env python3
"""
条形码识别入库管理系统安装脚本
自动检查环境、安装依赖并验证系统
"""

import sys
import os
import subprocess
import platform
from pathlib import Path


def print_header():
    """打印安装程序头部信息"""
    print("=" * 60)
    print("条形码识别入库管理系统 - 安装程序")
    print("版本: 1.0.0")
    print("=" * 60)


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_pip():
    """检查pip是否可用"""
    print("检查pip...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip不可用")
        return False


def install_dependencies():
    """安装依赖包"""
    print("安装依赖包...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        # 升级pip
        print("  升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("  安装项目依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    print("创建项目目录...")
    
    directories = ["data", "assets", "assets/icons", "tests"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory}")
    
    return True


def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    test_modules = [
        ("cv2", "OpenCV"),
        ("pyzbar", "pyzbar"),
        ("PIL", "Pillow"),
        ("numpy", "NumPy"),
        ("tkinter", "tkinter")
    ]
    
    failed_modules = []
    
    for module_name, display_name in test_modules:
        try:
            if module_name == "PIL":
                from PIL import Image
            else:
                __import__(module_name)
            print(f"  ✅ {display_name}")
        except ImportError:
            print(f"  ❌ {display_name}")
            failed_modules.append(display_name)
    
    if failed_modules:
        print(f"❌ 以下模块导入失败: {', '.join(failed_modules)}")
        return False
    
    return True


def test_system_components():
    """测试系统组件"""
    print("测试系统组件...")
    
    try:
        # 测试主程序
        result = subprocess.run([sys.executable, "main.py", "--test"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 系统组件测试通过")
            return True
        else:
            print("❌ 系统组件测试失败")
            print(f"   错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 系统组件测试超时")
        return False
    except Exception as e:
        print(f"❌ 系统组件测试异常: {e}")
        return False


def check_camera():
    """检查摄像头可用性"""
    print("检查摄像头...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                print("✅ 摄像头可用")
                return True
            else:
                print("⚠️  摄像头无法读取画面")
                return False
        else:
            print("⚠️  摄像头无法打开")
            return False
            
    except Exception as e:
        print(f"⚠️  摄像头检查失败: {e}")
        return False


def print_system_info():
    """打印系统信息"""
    print("\n系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {sys.version}")
    print(f"  安装路径: {os.getcwd()}")


def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "=" * 60)
    print("安装完成！使用说明:")
    print("=" * 60)
    print("1. 启动GUI界面:")
    print("   python main.py")
    print()
    print("2. 命令行模式:")
    print("   python main.py --cli")
    print()
    print("3. 扫描图片:")
    print("   python main.py --cli --image your_image.jpg")
    print()
    print("4. 运行测试:")
    print("   python main.py --test")
    print()
    print("5. 查看帮助:")
    print("   python main.py --help")
    print("=" * 60)


def main():
    """主安装流程"""
    print_header()
    print_system_info()
    print()
    
    # 检查步骤
    checks = [
        ("Python版本", check_python_version),
        ("pip工具", check_pip),
        ("安装依赖", install_dependencies),
        ("创建目录", create_directories),
        ("模块导入", test_imports),
        ("系统组件", test_system_components),
    ]
    
    failed_checks = []
    
    for check_name, check_func in checks:
        print(f"\n{check_name}...")
        if not check_func():
            failed_checks.append(check_name)
    
    # 可选检查
    print(f"\n可选检查...")
    check_camera()
    
    # 安装结果
    print("\n" + "=" * 60)
    if failed_checks:
        print("❌ 安装失败")
        print(f"失败的检查项: {', '.join(failed_checks)}")
        print("\n请解决上述问题后重新运行安装程序")
        return False
    else:
        print("✅ 安装成功")
        print_usage_instructions()
        return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n安装过程中发生错误: {e}")
        sys.exit(1)
