# 条形码识别入库管理系统

## 项目简介
这是一个基于Python开发的条形码识别入库管理系统，专门用于识别设备上的SN码条形码，实现设备信息的自动化入库管理。

## 主要功能
- 📷 支持摄像头实时扫描条形码
- 🖼️ 支持图片文件条形码识别
- 📊 设备信息数据库管理
- 🔍 条形码识别结果展示
- 📝 设备入库记录管理
- 🎯 支持多种条形码格式（Code128、Code39、EAN13等）
- 🔧 图像预处理和增强功能
- 📈 扫描历史记录和统计

## 技术栈
- **Python 3.8+**
- **OpenCV** - 图像处理
- **pyzbar** - 条形码识别
- **tkinter** - GUI界面
- **SQLite** - 数据存储
- **PIL** - 图像处理

## 系统要求
- Python 3.8 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux
- 摄像头（可选，用于实时扫描）
- 至少 100MB 可用磁盘空间

## 安装步骤

### 1. 克隆或下载项目
```bash
git clone <repository-url>
cd 条形码项目
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 验证安装
```bash
python main.py --test
```

## 使用方法

### GUI模式（推荐）
```bash
python main.py
```

### 命令行模式
```bash
# 摄像头扫描
python main.py --cli

# 扫描图片文件
python main.py --cli --image path/to/your/image.jpg
```

### 测试系统
```bash
python main.py --test
```

## 功能详解

### 1. 条形码识别
- 支持多种条形码格式：Code128, Code39, Code93, EAN13, EAN8, UPC-A, UPC-E, ITF, PDF417, QR码等
- 自动图像预处理和增强
- 实时摄像头扫描
- 批量图片处理

### 2. 设备管理
- 设备信息录入和编辑
- SN码唯一性验证
- 设备状态管理
- 批量导入导出

### 3. 数据库功能
- SQLite数据库存储
- 设备信息表和扫描历史表
- 数据统计和报表
- 数据备份和恢复

### 4. 图像处理
- 自动对比度和亮度调整
- 图像去噪和锐化
- 条形码区域检测
- 图像旋转和裁剪

## 项目结构
```
条形码项目/
├── main.py                 # 主程序入口
├── modules/
│   ├── __init__.py
│   ├── barcode_scanner.py  # 条形码识别模块
│   ├── database.py         # 数据库操作模块
│   ├── gui.py             # GUI界面模块
│   └── image_processor.py  # 图像处理模块
├── data/
│   └── devices.db         # SQLite数据库文件
├── assets/
│   └── icons/             # 图标资源
├── tests/
│   └── test_barcode_system.py  # 测试文件
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明
└── barcode_system.log    # 系统日志文件
```

## 使用指南

### 首次使用
1. 启动程序：`python main.py`
2. 程序会自动创建数据库和必要目录
3. 选择"选择图片"或"启动摄像头"开始扫描
4. 扫描成功后，在右侧填写设备信息
5. 点击"添加设备"保存到数据库

### 摄像头扫描
1. 点击"启动摄像头"按钮
2. 将条形码对准摄像头
3. 按's'键进行扫描，按'q'键退出
4. 扫描成功后会自动填充SN码

### 图片扫描
1. 点击"选择图片"按钮
2. 选择包含条形码的图片文件
3. 点击"扫描条形码"按钮
4. 如果识别效果不佳，可以先点击"图像增强"

### 设备管理
- **添加设备**：填写设备信息后点击"添加设备"
- **更新设备**：双击设备列表中的项目，修改后点击"更新设备"
- **删除设备**：选择设备后点击"删除设备"
- **刷新列表**：点击"刷新列表"更新设备显示

## 故障排除

### 常见问题

1. **摄像头无法启动**
   - 检查摄像头是否被其他程序占用
   - 确认摄像头驱动正常安装
   - 尝试更换摄像头索引（修改代码中的camera_index参数）

2. **条形码识别失败**
   - 确保条形码清晰可见
   - 尝试使用"图像增强"功能
   - 调整光照条件
   - 检查条形码格式是否支持

3. **依赖包安装失败**
   - 升级pip：`pip install --upgrade pip`
   - 使用国内镜像：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`
   - 检查Python版本是否符合要求

4. **数据库错误**
   - 检查data目录权限
   - 删除损坏的数据库文件，程序会自动重建
   - 查看日志文件获取详细错误信息

### 日志文件
系统会自动生成日志文件`barcode_system.log`，包含详细的运行信息和错误记录。

## 开发和测试

### 运行测试
```bash
python tests/test_barcode_system.py
```

### 代码结构
- `modules/barcode_scanner.py`：条形码识别核心功能
- `modules/database.py`：数据库操作和管理
- `modules/image_processor.py`：图像预处理和增强
- `modules/gui.py`：图形用户界面

### 扩展开发
系统采用模块化设计，可以轻松扩展新功能：
- 添加新的条形码格式支持
- 集成其他数据库系统
- 添加网络功能和远程管理
- 集成打印和标签功能

## 许可证
本项目采用MIT许可证，详见LICENSE文件。

## 贡献
欢迎提交Issue和Pull Request来改进项目。

## 联系方式
如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

## 更新日志

### v1.0.0 (2025-08-19)
- 初始版本发布
- 基础条形码识别功能
- GUI界面和数据库管理
- 图像处理和增强功能
- 完整的测试套件

---

**感谢使用条形码识别入库管理系统！**
