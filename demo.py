#!/usr/bin/env python3
"""
条形码识别入库管理系统演示脚本
展示系统的主要功能
"""

import sys
import os
import numpy as np
import cv2
from PIL import Image, ImageDraw, ImageFont

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner
from modules.database import DeviceDatabase
from modules.image_processor import ImageProcessor


def create_demo_barcode_image():
    """创建演示用的条形码图片"""
    print("创建演示条形码图片...")
    
    try:
        # 创建一个简单的条形码模拟图片
        # 这里我们创建一个简单的黑白条纹图案来模拟条形码
        width, height = 400, 100
        image = np.ones((height, width, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 创建简单的条纹图案（模拟条形码）
        bar_width = 4
        for i in range(0, width, bar_width * 2):
            # 黑色条纹
            image[:, i:i+bar_width] = 0
        
        # 添加文本标签
        pil_image = Image.fromarray(image)
        draw = ImageDraw.Draw(pil_image)
        
        try:
            # 尝试使用默认字体
            font = ImageFont.load_default()
        except:
            font = None
        
        # 添加文本
        text = "DEMO-SN-123456789"
        if font:
            draw.text((50, height + 10), text, fill=(0, 0, 0), font=font)
        
        # 保存图片
        demo_image_path = "demo_barcode.png"
        pil_image.save(demo_image_path)
        
        print(f"✅ 演示条形码图片已创建: {demo_image_path}")
        return demo_image_path
        
    except Exception as e:
        print(f"❌ 创建演示图片失败: {e}")
        return None


def demo_barcode_scanner():
    """演示条形码扫描功能"""
    print("\n" + "="*50)
    print("演示条形码扫描功能")
    print("="*50)
    
    scanner = BarcodeScanner()
    
    # 显示支持的格式
    formats = scanner.get_supported_formats()
    print(f"支持的条形码格式 ({len(formats)} 种):")
    for i, fmt in enumerate(formats, 1):
        print(f"  {i:2d}. {fmt}")
    
    # 测试数据验证
    print("\n测试条形码数据验证:")
    test_data = [
        ("123456789", None, "通用数据"),
        ("1234567890123", "EAN13", "EAN13格式"),
        ("12345678", "EAN8", "EAN8格式"),
        ("", None, "空数据"),
        ("12", None, "过短数据")
    ]
    
    for data, format_type, description in test_data:
        result = scanner.validate_barcode_data(data, format_type)
        status = "✅" if result else "❌"
        print(f"  {status} {description}: '{data}' -> {result}")
    
    # 尝试扫描演示图片
    demo_image = create_demo_barcode_image()
    if demo_image:
        print(f"\n尝试扫描演示图片: {demo_image}")
        results = scanner.scan_from_image(demo_image)
        
        if results:
            print(f"✅ 识别到 {len(results)} 个条形码:")
            for i, result in enumerate(results, 1):
                print(f"  条形码 {i}:")
                print(f"    数据: {result['data']}")
                print(f"    类型: {result['type']}")
                print(f"    位置: {result['position']}")
        else:
            print("ℹ️  未识别到条形码（这是正常的，因为我们创建的是模拟图片）")


def demo_database():
    """演示数据库功能"""
    print("\n" + "="*50)
    print("演示数据库功能")
    print("="*50)
    
    # 使用演示数据库
    db = DeviceDatabase("demo_devices.db")
    
    # 显示初始统计
    stats = db.get_statistics()
    print(f"数据库初始统计: {stats}")
    
    # 添加演示设备
    demo_devices = [
        {
            'sn_code': 'DEMO-001',
            'device_name': '演示服务器1',
            'device_type': '服务器',
            'manufacturer': '演示厂商',
            'model': 'DEMO-SERVER-2024',
            'location': '机房A-01',
            'notes': '演示用设备'
        },
        {
            'sn_code': 'DEMO-002',
            'device_name': '演示交换机1',
            'device_type': '网络设备',
            'manufacturer': '演示网络',
            'model': 'DEMO-SWITCH-48P',
            'location': '机房A-02',
            'notes': '48口千兆交换机'
        },
        {
            'sn_code': 'DEMO-003',
            'device_name': '演示存储设备',
            'device_type': '存储',
            'manufacturer': '演示存储',
            'model': 'DEMO-STORAGE-10TB',
            'location': '机房B-01',
            'notes': '10TB存储阵列'
        }
    ]
    
    print("\n添加演示设备:")
    for device in demo_devices:
        result = db.add_device(device)
        status = "✅" if result else "❌"
        print(f"  {status} {device['sn_code']}: {device['device_name']}")
    
    # 查询设备
    print("\n查询设备信息:")
    device = db.get_device('DEMO-001')
    if device:
        print(f"  设备SN: {device['sn_code']}")
        print(f"  设备名称: {device['device_name']}")
        print(f"  设备类型: {device['device_type']}")
        print(f"  制造商: {device['manufacturer']}")
        print(f"  位置: {device['location']}")
    
    # 更新设备
    print("\n更新设备信息:")
    updates = {'location': '机房A-01-更新', 'notes': '已更新的演示设备'}
    result = db.update_device('DEMO-001', updates)
    status = "✅" if result else "❌"
    print(f"  {status} 更新设备 DEMO-001")
    
    # 添加扫描历史
    print("\n添加扫描历史:")
    scan_records = [
        {'sn_code': 'DEMO-001', 'scan_result': '扫描成功', 'operator': '演示用户1'},
        {'sn_code': 'DEMO-002', 'scan_result': '扫描成功', 'operator': '演示用户2'},
        {'sn_code': 'DEMO-003', 'scan_result': '扫描成功', 'operator': '演示用户1'}
    ]
    
    for record in scan_records:
        result = db.add_scan_history(record)
        status = "✅" if result else "❌"
        print(f"  {status} {record['sn_code']}: {record['scan_result']}")
    
    # 获取所有设备
    print("\n所有设备列表:")
    devices = db.get_all_devices()
    for i, device in enumerate(devices, 1):
        print(f"  {i}. {device['sn_code']} - {device['device_name']} ({device['device_type']})")
    
    # 获取扫描历史
    print("\n扫描历史记录:")
    history = db.get_scan_history(limit=5)
    for i, record in enumerate(history, 1):
        print(f"  {i}. {record['sn_code']} - {record['scan_result']} ({record['scan_time']})")
    
    # 最终统计
    final_stats = db.get_statistics()
    print(f"\n最终统计: {final_stats}")


def demo_image_processor():
    """演示图像处理功能"""
    print("\n" + "="*50)
    print("演示图像处理功能")
    print("="*50)
    
    processor = ImageProcessor()
    
    # 创建测试图像
    print("创建测试图像...")
    test_image = np.random.randint(0, 256, (200, 300, 3), dtype=np.uint8)
    
    # 测试各种处理功能
    processing_functions = [
        ("对比度增强", lambda img: processor.enhance_contrast(img, 1.5)),
        ("亮度增强", lambda img: processor.enhance_brightness(img, 1.2)),
        ("图像锐化", lambda img: processor.sharpen_image(img)),
        ("噪声去除", lambda img: processor.remove_noise(img)),
        ("图像旋转", lambda img: processor.rotate_image(img, 45)),
        ("大小调整", lambda img: processor.resize_image(img, (150, 100))),
        ("条形码预处理", lambda img: processor.preprocess_for_barcode(img)),
        ("自动增强", lambda img: processor.auto_enhance_for_barcode(img))
    ]
    
    print("\n测试图像处理功能:")
    for name, func in processing_functions:
        try:
            result = func(test_image)
            print(f"  ✅ {name}: 输出形状 {result.shape}")
        except Exception as e:
            print(f"  ❌ {name}: 失败 - {e}")
    
    # 测试ROI裁剪
    print("\n测试ROI裁剪:")
    roi = (50, 50, 100, 80)  # x, y, width, height
    try:
        cropped = processor.crop_roi(test_image, roi)
        print(f"  ✅ ROI裁剪: 原始 {test_image.shape} -> 裁剪后 {cropped.shape}")
    except Exception as e:
        print(f"  ❌ ROI裁剪失败: {e}")
    
    # 测试条形码区域检测
    print("\n测试条形码区域检测:")
    try:
        region = processor.detect_barcode_region(test_image)
        if region:
            print(f"  ✅ 检测到条形码区域: {region}")
        else:
            print("  ℹ️  未检测到条形码区域（正常，因为是随机图像）")
    except Exception as e:
        print(f"  ❌ 条形码区域检测失败: {e}")


def demo_integration():
    """演示系统集成功能"""
    print("\n" + "="*50)
    print("演示系统集成功能")
    print("="*50)
    
    # 初始化所有组件
    scanner = BarcodeScanner()
    db = DeviceDatabase("integration_demo.db")
    processor = ImageProcessor()
    
    print("✅ 所有组件初始化完成")
    
    # 模拟完整工作流程
    print("\n模拟完整工作流程:")
    
    # 1. 创建测试图像
    print("1. 创建测试图像...")
    test_image = np.zeros((200, 400, 3), dtype=np.uint8)
    test_image.fill(128)  # 灰色背景
    
    # 2. 图像预处理
    print("2. 图像预处理...")
    processed_image = processor.auto_enhance_for_barcode(test_image)
    print(f"   预处理完成: {test_image.shape} -> {processed_image.shape}")
    
    # 3. 条形码扫描
    print("3. 条形码扫描...")
    scan_results = scanner._decode_barcodes(test_image)
    print(f"   扫描结果: {len(scan_results)} 个条形码")
    
    # 4. 模拟设备信息
    print("4. 处理设备信息...")
    device_info = {
        'sn_code': 'INTEGRATION-DEMO-001',
        'device_name': '集成演示设备',
        'device_type': '测试设备',
        'manufacturer': '演示厂商',
        'model': 'INTEGRATION-MODEL-2024'
    }
    
    # 5. 添加到数据库
    print("5. 添加到数据库...")
    add_result = db.add_device(device_info)
    status = "✅" if add_result else "❌"
    print(f"   {status} 设备添加结果")
    
    # 6. 记录扫描历史
    print("6. 记录扫描历史...")
    scan_info = {
        'sn_code': 'INTEGRATION-DEMO-001',
        'scan_result': '集成演示扫描',
        'operator': '演示系统'
    }
    history_result = db.add_scan_history(scan_info)
    status = "✅" if history_result else "❌"
    print(f"   {status} 扫描历史记录")
    
    # 7. 验证结果
    print("7. 验证结果...")
    device = db.get_device('INTEGRATION-DEMO-001')
    if device:
        print(f"   ✅ 设备验证成功: {device['device_name']}")
    else:
        print("   ❌ 设备验证失败")
    
    print("\n✅ 集成演示完成")


def cleanup_demo_files():
    """清理演示文件"""
    print("\n清理演示文件...")
    
    demo_files = [
        "demo_barcode.png",
        "demo_devices.db",
        "integration_demo.db"
    ]
    
    for file_path in demo_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"  ✅ 已删除: {file_path}")
        except Exception as e:
            print(f"  ⚠️  删除失败 {file_path}: {e}")


def main():
    """主演示函数"""
    print("=" * 60)
    print("条形码识别入库管理系统 - 功能演示")
    print("版本: 1.0.0")
    print("=" * 60)
    
    try:
        # 运行各个演示
        demo_barcode_scanner()
        demo_database()
        demo_image_processor()
        demo_integration()
        
        print("\n" + "=" * 60)
        print("✅ 所有演示完成！")
        print("=" * 60)
        
        # 询问是否清理文件
        try:
            response = input("\n是否清理演示文件？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                cleanup_demo_files()
        except:
            pass  # 如果无法获取输入，跳过清理
        
        print("\n演示结束。要启动完整系统，请运行: python main.py")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        return False
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
        sys.exit(1)
