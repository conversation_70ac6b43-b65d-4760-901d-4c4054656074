"""
条形码识别模块
支持多种条形码格式的识别，包括Code128、Code39、EAN13等
"""

import cv2
import numpy as np
from pyzbar import pyzbar
from PIL import Image
import logging
import os
from typing import List, Dict, Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BarcodeScanner:
    """条形码扫描器类"""
    
    def __init__(self):
        """初始化条形码扫描器"""
        self.supported_formats = [
            'CODE128', 'CODE39', 'CODE93', 'CODABAR',
            'EAN8', 'EAN13', 'UPC_A', 'UPC_E',
            'ITF', 'PDF417', 'QRCODE', 'DATAMATRIX'
        ]
        logger.info("条形码扫描器初始化完成")
    
    def scan_from_image(self, image_path: str) -> List[Dict]:
        """
        从图片文件中识别条形码

        Args:
            image_path: 图片文件路径

        Returns:
            识别结果列表，每个结果包含条形码数据和类型
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return []

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图片: {image_path}")
                return []

            # 检查图片是否为空
            if image.size == 0:
                logger.error(f"图片为空: {image_path}")
                return []

            return self._decode_barcodes(image)

        except Exception as e:
            logger.error(f"图片条形码识别失败: {str(e)}")
            return []
    
    def scan_from_camera(self, camera_index: int = 0) -> Optional[List[Dict]]:
        """
        从摄像头实时识别条形码
        
        Args:
            camera_index: 摄像头索引，默认为0
            
        Returns:
            识别结果列表或None
        """
        try:
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                logger.error(f"无法打开摄像头: {camera_index}")
                return None
            
            logger.info("摄像头已启动，按'q'键退出，按's'键扫描")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    logger.error("无法读取摄像头画面")
                    break
                
                # 显示画面
                cv2.imshow('条形码扫描 - 按s扫描，按q退出', frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    # 扫描当前帧
                    results = self._decode_barcodes(frame)
                    if results:
                        cap.release()
                        cv2.destroyAllWindows()
                        return results
            
            cap.release()
            cv2.destroyAllWindows()
            return None
            
        except Exception as e:
            logger.error(f"摄像头条形码识别失败: {str(e)}")
            return None
    
    def _decode_barcodes(self, image: np.ndarray) -> List[Dict]:
        """
        解码图像中的条形码

        Args:
            image: OpenCV图像数组

        Returns:
            识别结果列表
        """
        results = []

        try:
            # 检查图像是否有效
            if image is None or image.size == 0:
                logger.error("传入的图像为空")
                return results

            # 检查图像维度
            if len(image.shape) < 2:
                logger.error("图像维度不正确")
                return results

            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 使用pyzbar解码条形码
            barcodes = pyzbar.decode(gray)

            for barcode in barcodes:
                try:
                    # 提取条形码数据
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type

                    # 获取条形码位置
                    (x, y, w, h) = barcode.rect

                    result = {
                        'data': barcode_data,
                        'type': barcode_type,
                        'position': (x, y, w, h),
                        'polygon': barcode.polygon
                    }

                    results.append(result)
                    logger.info(f"识别到条形码: {barcode_data} (类型: {barcode_type})")

                except Exception as decode_error:
                    logger.error(f"解码单个条形码失败: {str(decode_error)}")
                    continue

            if not results:
                logger.info("未识别到条形码")

        except Exception as e:
            logger.error(f"条形码解码失败: {str(e)}")

        return results
    
    def enhance_image_for_scanning(self, image: np.ndarray) -> np.ndarray:
        """
        增强图像以提高条形码识别率
        
        Args:
            image: 原始图像
            
        Returns:
            增强后的图像
        """
        try:
            # 转换为灰度图像
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 应用自适应阈值
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作去除噪声
            kernel = np.ones((3, 3), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"图像增强失败: {str(e)}")
            return image
    
    def validate_barcode_data(self, data: str, expected_format: str = None) -> bool:
        """
        验证条形码数据格式
        
        Args:
            data: 条形码数据
            expected_format: 期望的格式类型
            
        Returns:
            验证结果
        """
        if not data:
            return False
        
        # 基本长度检查
        if len(data) < 3:
            return False
        
        # 根据不同格式进行验证
        if expected_format:
            if expected_format.upper() == 'EAN13' and len(data) != 13:
                return False
            elif expected_format.upper() == 'EAN8' and len(data) != 8:
                return False
            elif expected_format.upper() in ['UPC_A'] and len(data) != 12:
                return False
        
        return True
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的条形码格式列表"""
        return self.supported_formats.copy()


# 测试函数
def test_barcode_scanner():
    """测试条形码扫描器功能"""
    scanner = BarcodeScanner()
    
    print("支持的条形码格式:")
    for fmt in scanner.get_supported_formats():
        print(f"  - {fmt}")
    
    print("\n测试完成")


if __name__ == "__main__":
    test_barcode_scanner()
