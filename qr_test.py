#!/usr/bin/env python3
"""
二维码测试脚本
演示系统的二维码识别功能
"""

import sys
import os
import json

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner


def test_qr_support():
    """测试二维码支持"""
    print("=" * 50)
    print("二维码支持测试")
    print("=" * 50)
    
    scanner = BarcodeScanner()
    
    # 显示支持的格式
    formats = scanner.get_supported_formats()
    qr_formats = [f for f in formats if any(keyword in f for keyword in ['QR', 'DATA', 'PDF'])]
    
    print(f"系统支持的二维码格式 ({len(qr_formats)} 种):")
    for fmt in qr_formats:
        print(f"  ✅ {fmt}")
    
    # 测试二维码数据验证
    print("\n二维码数据验证测试:")
    test_data = [
        "https://example.com/device/SN123456",
        '{"sn": "DEV001", "type": "server", "location": "A-01"}',
        "SN:DEV002|TYPE:SWITCH|LOC:B-02",
        "设备编号：中文测试123",
        "Very long QR code data with lots of information that can be stored in QR codes unlike traditional barcodes"
    ]
    
    for data in test_data:
        result = scanner.validate_barcode_data(data)
        status = "✅" if result else "❌"
        preview = data[:50] + "..." if len(data) > 50 else data
        print(f"  {status} {preview}")
    
    print("\n二维码的优势:")
    print("  📱 存储容量大 - 可存储数千字符")
    print("  🔧 支持中文和特殊字符")
    print("  📊 可存储结构化数据（JSON、XML等）")
    print("  🌐 支持URL链接")
    print("  🛡️ 内置错误纠正功能")
    print("  📐 可在小空间内存储大量信息")
    
    print("\n设备管理中的二维码应用场景:")
    print("  🏭 设备SN码 + 详细配置信息")
    print("  📋 维护记录 + 保修信息")
    print("  🔗 链接到设备管理系统")
    print("  📊 设备状态 + 实时监控数据")
    print("  📱 移动端快速访问设备信息")


def create_sample_qr_data():
    """创建示例二维码数据"""
    print("\n" + "=" * 50)
    print("示例二维码数据格式")
    print("=" * 50)
    
    # 简单SN码
    simple_sn = "SN:SERVER001"
    print(f"1. 简单SN码格式:")
    print(f"   {simple_sn}")
    
    # 结构化数据
    device_info = {
        "sn": "SERVER001",
        "name": "生产服务器01",
        "type": "服务器",
        "manufacturer": "Dell",
        "model": "PowerEdge R740",
        "location": "机房A-机柜01-U10",
        "ip": "*************",
        "purchase_date": "2024-01-15",
        "warranty_end": "2027-01-15",
        "contact": "张工 13800138000"
    }
    
    json_data = json.dumps(device_info, ensure_ascii=False, indent=2)
    print(f"\n2. JSON结构化数据:")
    print(json_data)
    
    # URL格式
    url_format = f"https://device-mgmt.company.com/device/{device_info['sn']}"
    print(f"\n3. URL链接格式:")
    print(f"   {url_format}")
    
    # 管道分隔格式
    pipe_format = f"SN:{device_info['sn']}|NAME:{device_info['name']}|TYPE:{device_info['type']}|LOC:{device_info['location']}"
    print(f"\n4. 管道分隔格式:")
    print(f"   {pipe_format}")
    
    print(f"\n推荐使用JSON格式，因为:")
    print("  ✅ 结构清晰，易于解析")
    print("  ✅ 支持中文和特殊字符")
    print("  ✅ 可扩展性强")
    print("  ✅ 标准化格式")


def demo_qr_workflow():
    """演示二维码工作流程"""
    print("\n" + "=" * 50)
    print("二维码工作流程演示")
    print("=" * 50)
    
    print("在实际使用中，二维码工作流程如下:")
    print("\n1. 📱 扫描设备上的二维码")
    print("   - 使用摄像头实时扫描")
    print("   - 或上传二维码图片")
    
    print("\n2. 🔍 系统自动识别二维码内容")
    print("   - 支持QR码、DataMatrix等格式")
    print("   - 自动解析数据内容")
    
    print("\n3. 📊 解析设备信息")
    print("   - 如果是JSON格式，自动解析各字段")
    print("   - 如果是简单格式，提取SN码")
    print("   - 如果是URL，可以跳转获取更多信息")
    
    print("\n4. 💾 保存到数据库")
    print("   - 自动填充设备信息表单")
    print("   - 记录扫描历史")
    print("   - 更新设备状态")
    
    print("\n5. 📈 后续管理")
    print("   - 查看设备详情")
    print("   - 更新设备信息")
    print("   - 生成统计报表")


def main():
    """主函数"""
    print("🔲 二维码功能测试和演示")
    print("系统完全支持二维码识别！")
    
    test_qr_support()
    create_sample_qr_data()
    demo_qr_workflow()
    
    print("\n" + "=" * 50)
    print("✅ 二维码功能完全可用！")
    print("=" * 50)
    print("\n要测试二维码识别，请:")
    print("1. 准备一个包含二维码的图片")
    print("2. 运行: python main.py --cli --image your_qr_image.jpg")
    print("3. 或启动GUI界面: python main.py")
    print("4. 使用摄像头扫描二维码")


if __name__ == "__main__":
    main()
