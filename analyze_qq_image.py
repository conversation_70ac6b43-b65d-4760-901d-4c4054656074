#!/usr/bin/env python3
"""
专门分析qq.png图片的工具
尝试多种增强方法来识别二维码
"""

import sys
import os
import cv2
import numpy as np

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner
from modules.image_processor import ImageProcessor


def analyze_qq_image():
    """深度分析qq.png图片"""
    print("🔍 深度分析 qq.png 图片")
    print("=" * 50)
    
    # 查找图片文件
    possible_paths = [
        "qq.png",
        "C:/Users/<USER>/Desktop/qq.png",
        "../qq.png"
    ]
    
    image_path = None
    for path in possible_paths:
        if os.path.exists(path):
            image_path = path
            break
    
    if not image_path:
        print("❌ 未找到 qq.png 文件")
        return False
    
    print(f"📁 分析图片: {image_path}")
    
    # 初始化组件
    scanner = BarcodeScanner()
    processor = ImageProcessor()
    
    try:
        # 读取原始图片
        image = cv2.imread(image_path)
        if image is None:
            print("❌ 无法读取图片")
            return False
        
        height, width = image.shape[:2]
        print(f"📐 图片尺寸: {width} x {height}")
        print(f"📊 图片大小: {image.size} 像素")
        
        # 分析图片特征
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算图片统计信息
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        min_brightness = np.min(gray)
        max_brightness = np.max(gray)
        
        print(f"🌟 亮度统计:")
        print(f"   平均亮度: {mean_brightness:.1f}")
        print(f"   亮度标准差: {std_brightness:.1f}")
        print(f"   最暗像素: {min_brightness}")
        print(f"   最亮像素: {max_brightness}")
        
        # 判断图片特征
        if std_brightness < 30:
            print("⚠️  图片对比度较低")
        if mean_brightness < 80:
            print("⚠️  图片整体较暗")
        elif mean_brightness > 180:
            print("⚠️  图片整体较亮")
        
        print(f"\n🔬 尝试多种识别方法...")
        
        # 方法列表
        methods = [
            ("原始图片", lambda img: img),
            ("标准增强", lambda img: processor.auto_enhance_for_barcode(img)),
            ("强对比度增强", lambda img: processor.enhance_contrast(img, 2.5)),
            ("超强对比度增强", lambda img: processor.enhance_contrast(img, 3.0)),
            ("亮度+对比度", lambda img: processor.enhance_contrast(
                processor.enhance_brightness(img, 1.5), 2.0)),
            ("锐化处理", lambda img: processor.sharpen_image(img)),
            ("强锐化", lambda img: processor.sharpen_image(
                processor.sharpen_image(img))),
            ("去噪+锐化", lambda img: processor.sharpen_image(
                processor.remove_noise(img))),
        ]
        
        # 尺寸调整方法
        scale_methods = [
            ("2倍放大", 2.0),
            ("1.5倍放大", 1.5),
            ("0.8倍缩小", 0.8),
            ("0.5倍缩小", 0.5),
        ]
        
        success_count = 0
        
        # 测试基本方法
        for method_name, method_func in methods:
            print(f"\n🔄 测试: {method_name}")
            
            try:
                processed = method_func(image)
                
                # 处理灰度图像
                if len(processed.shape) == 2:
                    from pyzbar import pyzbar
                    barcodes = pyzbar.decode(processed)
                    
                    if barcodes:
                        print(f"   ✅ {method_name} 识别成功!")
                        for i, barcode in enumerate(barcodes, 1):
                            barcode_data = barcode.data.decode('utf-8')
                            barcode_type = barcode.type
                            print(f"      📱 码 {i}: {barcode_data} (类型: {barcode_type})")
                        success_count += 1
                    else:
                        print(f"   ❌ {method_name} 识别失败")
                else:
                    results = scanner._decode_barcodes(processed)
                    if results:
                        print(f"   ✅ {method_name} 识别成功!")
                        for i, result in enumerate(results, 1):
                            print(f"      📱 码 {i}: {result['data']} (类型: {result['type']})")
                        success_count += 1
                    else:
                        print(f"   ❌ {method_name} 识别失败")
                        
            except Exception as e:
                print(f"   ❌ {method_name} 处理出错: {e}")
        
        # 测试尺寸调整方法
        print(f"\n🔄 测试尺寸调整方法...")
        
        for scale_name, scale in scale_methods:
            print(f"\n🔄 测试: {scale_name}")
            
            try:
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                resized = cv2.resize(image, (new_width, new_height), 
                                   interpolation=cv2.INTER_CUBIC)
                
                # 对调整后的图片应用增强
                enhanced = processor.auto_enhance_for_barcode(resized)
                
                from pyzbar import pyzbar
                barcodes = pyzbar.decode(enhanced)
                
                if barcodes:
                    print(f"   ✅ {scale_name} 识别成功!")
                    for i, barcode in enumerate(barcodes, 1):
                        barcode_data = barcode.data.decode('utf-8')
                        barcode_type = barcode.type
                        print(f"      📱 码 {i}: {barcode_data} (类型: {barcode_type})")
                    success_count += 1
                else:
                    print(f"   ❌ {scale_name} 识别失败")
                    
            except Exception as e:
                print(f"   ❌ {scale_name} 处理出错: {e}")
        
        # 测试旋转方法
        print(f"\n🔄 测试旋转方法...")
        
        angles = [90, 180, 270, 45, -45, 30, -30]
        for angle in angles:
            try:
                rotated = processor.rotate_image(image, angle)
                enhanced = processor.auto_enhance_for_barcode(rotated)
                
                from pyzbar import pyzbar
                barcodes = pyzbar.decode(enhanced)
                
                if barcodes:
                    print(f"   ✅ 旋转{angle}度 识别成功!")
                    for i, barcode in enumerate(barcodes, 1):
                        barcode_data = barcode.data.decode('utf-8')
                        barcode_type = barcode.type
                        print(f"      📱 码 {i}: {barcode_data} (类型: {barcode_type})")
                    success_count += 1
                    break
                    
            except Exception as e:
                continue
        
        # 总结
        print(f"\n" + "=" * 50)
        print(f"📊 分析结果:")
        print(f"   测试方法总数: {len(methods) + len(scale_methods) + len(angles)}")
        print(f"   成功识别次数: {success_count}")
        
        if success_count > 0:
            print(f"✅ 图片中确实包含可识别的二维码/条形码")
            return True
        else:
            print(f"❌ 所有方法都无法识别此图片")
            print(f"\n💡 可能的原因:")
            print(f"   - 图片中没有二维码/条形码")
            print(f"   - 二维码/条形码严重损坏或模糊")
            print(f"   - 二维码/条形码太小")
            print(f"   - 图片质量过低")
            
            print(f"\n🔧 建议:")
            print(f"   - 尝试使用更清晰的图片")
            print(f"   - 确保二维码/条形码完整可见")
            print(f"   - 使用已知可识别的测试图片")
            
            return False
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        return False


def main():
    """主函数"""
    success = analyze_qq_image()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎉 分析完成 - 找到了识别方法!")
    else:
        print("😔 分析完成 - 建议更换图片")
        print("\n📝 您可以:")
        print("   1. 尝试使用之前成功的图片:")
        print("      A44D407E-7666-4D26-90F1-D67E70FDEC4B-60305-000011.jpg")
        print("   2. 使用更清晰的二维码图片")
        print("   3. 在GUI中继续尝试不同的增强设置")


if __name__ == "__main__":
    main()
