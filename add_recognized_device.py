#!/usr/bin/env python3
"""
将识别到的二维码数据添加到设备管理系统
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.database import DeviceDatabase


def add_qr_device():
    """添加识别到的二维码设备"""
    print("=" * 50)
    print("添加识别到的设备信息")
    print("=" * 50)
    
    # 识别到的二维码数据
    qr_data = "03012201000224070030621"
    
    print(f"识别到的二维码数据: {qr_data}")
    
    # 初始化数据库
    db = DeviceDatabase()
    
    # 创建设备信息
    device_info = {
        'sn_code': qr_data,
        'device_name': '二维码识别设备',
        'device_type': '未知设备',
        'manufacturer': '待确认',
        'model': '待确认',
        'barcode_type': 'QRCODE',
        'location': '待确认',
        'notes': f'通过二维码识别添加，原始数据: {qr_data}'
    }
    
    # 添加设备
    print("\n添加设备到数据库...")
    success = db.add_device(device_info)
    
    if success:
        print("✅ 设备添加成功！")
        
        # 添加扫描历史
        scan_info = {
            'sn_code': qr_data,
            'scan_result': '二维码识别成功',
            'image_path': 'A44D407E-7666-4D26-90F1-D67E70FDEC4B-60305-000011.jpg',
            'operator': '系统自动识别'
        }
        
        db.add_scan_history(scan_info)
        print("✅ 扫描历史记录已添加")
        
        # 显示设备信息
        print("\n设备信息:")
        device = db.get_device(qr_data)
        if device:
            for key, value in device.items():
                if key not in ['id', 'created_at', 'updated_at']:
                    print(f"  {key}: {value}")
        
    else:
        print("❌ 设备添加失败（可能SN码已存在）")
        
        # 检查是否已存在
        existing = db.get_device(qr_data)
        if existing:
            print("该设备已存在于数据库中:")
            print(f"  设备名称: {existing['device_name']}")
            print(f"  设备类型: {existing['device_type']}")
            print(f"  添加时间: {existing['created_at']}")


if __name__ == "__main__":
    add_qr_device()
