#!/usr/bin/env python3
"""
条形码识别入库管理系统
主程序入口

功能特性:
- 支持摄像头实时扫描条形码
- 支持图片文件条形码识别
- 设备信息数据库管理
- 条形码识别结果展示
- 设备入库记录管理
- 支持多种条形码格式

作者: AI Assistant
版本: 1.0.0
日期: 2025-08-19
"""

import sys
import os
import logging
import argparse
from pathlib import Path

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gui import BarcodeGUI
from modules.barcode_scanner import BarcodeScanner
from modules.database import DeviceDatabase
from modules.image_processor import ImageProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('barcode_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'cv2', 'pyzbar', 'PIL', 'numpy', 'tkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'pyzbar':
                import pyzbar
            elif package == 'PIL':
                from PIL import Image
            elif package == 'numpy':
                import numpy
            elif package == 'tkinter':
                import tkinter
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    return True


def create_directories():
    """创建必要的目录"""
    directories = ['data', 'assets', 'assets/icons', 'tests']
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"确保目录存在: {directory}")


def test_components():
    """测试系统组件"""
    logger.info("开始测试系统组件...")
    
    try:
        # 测试条形码扫描器
        logger.info("测试条形码扫描器...")
        scanner = BarcodeScanner()
        formats = scanner.get_supported_formats()
        logger.info(f"支持的条形码格式: {len(formats)} 种")
        
        # 测试数据库
        logger.info("测试数据库连接...")
        db = DeviceDatabase()
        stats = db.get_statistics()
        logger.info(f"数据库统计: {stats}")
        
        # 测试图像处理器
        logger.info("测试图像处理器...")
        processor = ImageProcessor()
        logger.info("图像处理器初始化成功")
        
        logger.info("所有组件测试通过")
        return True
        
    except Exception as e:
        logger.error(f"组件测试失败: {str(e)}")
        return False


def run_gui_mode():
    """运行GUI模式"""
    logger.info("启动GUI模式...")
    
    try:
        app = BarcodeGUI()
        app.run()
        
    except Exception as e:
        logger.error(f"GUI模式运行失败: {str(e)}")
        return False
    
    return True


def run_cli_mode(image_path: str = None):
    """运行命令行模式"""
    logger.info("启动命令行模式...")
    
    try:
        scanner = BarcodeScanner()
        
        if image_path:
            # 扫描指定图片
            logger.info(f"扫描图片: {image_path}")
            results = scanner.scan_from_image(image_path)
            
            if results:
                print(f"\n识别到 {len(results)} 个条形码:")
                for i, result in enumerate(results, 1):
                    print(f"  条形码 {i}:")
                    print(f"    数据: {result['data']}")
                    print(f"    类型: {result['type']}")
                    print(f"    位置: {result['position']}")
            else:
                print("未识别到条形码")
        else:
            # 摄像头扫描
            logger.info("启动摄像头扫描...")
            results = scanner.scan_from_camera()
            
            if results:
                print(f"\n识别到 {len(results)} 个条形码:")
                for i, result in enumerate(results, 1):
                    print(f"  条形码 {i}:")
                    print(f"    数据: {result['data']}")
                    print(f"    类型: {result['type']}")
                    print(f"    位置: {result['position']}")
            else:
                print("未识别到条形码或用户取消")
        
        return True
        
    except Exception as e:
        logger.error(f"命令行模式运行失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="条形码识别入库管理系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动GUI界面
  python main.py --cli              # 启动命令行模式（摄像头）
  python main.py --cli --image path/to/image.jpg  # 扫描指定图片
  python main.py --test             # 测试系统组件
        """
    )
    
    parser.add_argument('--cli', action='store_true', 
                       help='使用命令行模式而不是GUI')
    parser.add_argument('--image', type=str, 
                       help='要扫描的图片文件路径（仅CLI模式）')
    parser.add_argument('--test', action='store_true', 
                       help='测试系统组件')
    parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
    
    args = parser.parse_args()
    
    # 显示启动信息
    print("=" * 60)
    print("条形码识别入库管理系统 v1.0.0")
    print("支持多种条形码格式的识别和设备管理")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    # 根据参数选择运行模式
    if args.test:
        # 测试模式
        success = test_components()
        sys.exit(0 if success else 1)
        
    elif args.cli:
        # 命令行模式
        success = run_cli_mode(args.image)
        sys.exit(0 if success else 1)
        
    else:
        # GUI模式（默认）
        success = run_gui_mode()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        print("\n程序已退出")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        print(f"错误: {str(e)}")
        sys.exit(1)
