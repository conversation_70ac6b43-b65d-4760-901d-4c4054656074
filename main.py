#!/usr/bin/env python3
"""
条形码识别入库管理系统
主程序入口

功能特性:
- 支持摄像头实时扫描条形码
- 支持图片文件条形码识别
- 设备信息数据库管理
- 条形码识别结果展示
- 设备入库记录管理
- 支持多种条形码格式

作者: AI Assistant
版本: 1.0.0
日期: 2025-08-19
"""

import sys
import os
import logging
import argparse
from pathlib import Path

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.gui import BarcodeGUI
from modules.barcode_scanner import BarcodeScanner
from modules.database import DeviceDatabase
from modules.image_processor import ImageProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('barcode_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'cv2', 'pyzbar', 'PIL', 'numpy', 'tkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'pyzbar':
                import pyzbar
            elif package == 'PIL':
                from PIL import Image
            elif package == 'numpy':
                import numpy
            elif package == 'tkinter':
                import tkinter
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    return True


def create_directories():
    """创建必要的目录"""
    directories = ['data', 'assets', 'assets/icons', 'tests']
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"确保目录存在: {directory}")


def test_components():
    """测试系统组件"""
    logger.info("开始测试系统组件...")
    
    try:
        # 测试条形码扫描器
        logger.info("测试条形码扫描器...")
        scanner = BarcodeScanner()
        formats = scanner.get_supported_formats()
        logger.info(f"支持的条形码格式: {len(formats)} 种")
        
        # 测试数据库
        logger.info("测试数据库连接...")
        db = DeviceDatabase()
        stats = db.get_statistics()
        logger.info(f"数据库统计: {stats}")
        
        # 测试图像处理器
        logger.info("测试图像处理器...")
        processor = ImageProcessor()
        logger.info("图像处理器初始化成功")
        
        logger.info("所有组件测试通过")
        return True
        
    except Exception as e:
        logger.error(f"组件测试失败: {str(e)}")
        return False


def run_enhance_test(image_path: str):
    """运行图像增强测试模式"""
    logger.info(f"启动图像增强测试模式: {image_path}")

    try:
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return False

        scanner = BarcodeScanner()
        processor = ImageProcessor()

        print(f"🔍 测试图片: {image_path}")
        print("=" * 50)

        # 读取图片
        import cv2
        image = cv2.imread(image_path)
        if image is None:
            print("❌ 无法读取图片文件")
            return False

        height, width = image.shape[:2]
        print(f"📐 图片尺寸: {width} x {height}")

        # 1. 直接识别
        print("\n1️⃣ 直接识别...")
        results = scanner._decode_barcodes(image)
        if results:
            print("✅ 直接识别成功!")
            for i, result in enumerate(results, 1):
                print(f"   码 {i}: {result['data']} (类型: {result['type']})")
            return True
        else:
            print("   未能直接识别，尝试增强...")

        # 2. 图像增强识别
        enhancement_methods = [
            ("锐化处理", lambda img: processor.sharpen_image(img)),
            ("自动增强", lambda img: processor.auto_enhance_for_barcode(img)),
            ("对比度增强", lambda img: processor.enhance_contrast(img, 1.8)),
            ("亮度增强", lambda img: processor.enhance_brightness(img, 1.3)),
        ]

        for method_name, enhance_func in enhancement_methods:
            print(f"\n2️⃣ {method_name}...")
            try:
                enhanced = enhance_func(image)

                # 处理灰度图像
                if len(enhanced.shape) == 2:
                    from pyzbar import pyzbar
                    barcodes = pyzbar.decode(enhanced)
                    if barcodes:
                        print(f"✅ {method_name}识别成功!")
                        for i, barcode in enumerate(barcodes, 1):
                            barcode_data = barcode.data.decode('utf-8')
                            barcode_type = barcode.type
                            print(f"   码 {i}: {barcode_data} (类型: {barcode_type})")
                        return True
                else:
                    results = scanner._decode_barcodes(enhanced)
                    if results:
                        print(f"✅ {method_name}识别成功!")
                        for i, result in enumerate(results, 1):
                            print(f"   码 {i}: {result['data']} (类型: {result['type']})")
                        return True

                print(f"   {method_name}识别失败")

            except Exception as e:
                print(f"   {method_name}处理出错: {e}")

        print("\n❌ 所有增强方法都无法识别")
        print("💡 建议: 尝试更清晰的图片或在GUI中手动调整")
        return False

    except Exception as e:
        logger.error(f"图像增强测试失败: {str(e)}")
        return False


def run_gui_mode():
    """运行GUI模式"""
    logger.info("启动GUI模式...")

    try:
        app = BarcodeGUI()
        app.run()

    except Exception as e:
        logger.error(f"GUI模式运行失败: {str(e)}")
        return False

    return True


def run_cli_mode(image_path: str = None):
    """运行命令行模式"""
    logger.info("启动命令行模式...")
    
    try:
        scanner = BarcodeScanner()
        
        if image_path:
            # 扫描指定图片
            logger.info(f"扫描图片: {image_path}")
            results = scanner.scan_from_image(image_path)
            
            if results:
                print(f"\n识别到 {len(results)} 个条形码:")
                for i, result in enumerate(results, 1):
                    print(f"  条形码 {i}:")
                    print(f"    数据: {result['data']}")
                    print(f"    类型: {result['type']}")
                    print(f"    位置: {result['position']}")
            else:
                print("未识别到条形码")
        else:
            # 摄像头扫描
            logger.info("启动摄像头扫描...")
            results = scanner.scan_from_camera()
            
            if results:
                print(f"\n识别到 {len(results)} 个条形码:")
                for i, result in enumerate(results, 1):
                    print(f"  条形码 {i}:")
                    print(f"    数据: {result['data']}")
                    print(f"    类型: {result['type']}")
                    print(f"    位置: {result['position']}")
            else:
                print("未识别到条形码或用户取消")
        
        return True
        
    except Exception as e:
        logger.error(f"命令行模式运行失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="条形码识别入库管理系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动GUI界面
  python main.py --cli              # 启动命令行模式（摄像头）
  python main.py --cli --image path/to/image.jpg  # 扫描指定图片
  python main.py --test             # 测试系统组件
  python main.py --enhance-test image.jpg  # 测试图像增强识别
        """
    )

    parser.add_argument('--cli', action='store_true',
                       help='使用命令行模式而不是GUI')
    parser.add_argument('--image', type=str,
                       help='要扫描的图片文件路径（仅CLI模式）')
    parser.add_argument('--test', action='store_true',
                       help='测试系统组件')
    parser.add_argument('--enhance-test', type=str, metavar='IMAGE',
                       help='测试图像增强识别功能')
    parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
    
    args = parser.parse_args()
    
    # 显示启动信息
    print("=" * 60)
    print("条形码识别入库管理系统 v1.0.0")
    print("支持多种条形码格式的识别和设备管理")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    # 根据参数选择运行模式
    if args.test:
        # 测试模式
        success = test_components()
        sys.exit(0 if success else 1)

    elif args.enhance_test:
        # 图像增强测试模式
        success = run_enhance_test(args.enhance_test)
        sys.exit(0 if success else 1)

    elif args.cli:
        # 命令行模式
        success = run_cli_mode(args.image)
        sys.exit(0 if success else 1)

    else:
        # GUI模式（默认）
        success = run_gui_mode()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        print("\n程序已退出")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        print(f"错误: {str(e)}")
        sys.exit(1)
