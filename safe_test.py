#!/usr/bin/env python3
"""
安全的二维码识别测试脚本
修复了OpenCV错误问题
"""

import sys
import os
import cv2
import numpy as np

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner
from modules.image_processor import ImageProcessor


def safe_test_image(image_path):
    """安全地测试图片识别"""
    print(f"🔍 测试图片: {image_path}")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 错误: 图片文件不存在")
        print(f"   请确保文件 '{image_path}' 在当前目录中")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(image_path)
    print(f"📁 文件大小: {file_size} 字节")
    
    if file_size == 0:
        print("❌ 错误: 文件为空")
        return False
    
    try:
        # 初始化组件
        print("🔧 初始化识别组件...")
        scanner = BarcodeScanner()
        processor = ImageProcessor()
        
        # 安全读取图片
        print("📖 读取图片...")
        image = cv2.imread(image_path)
        
        if image is None:
            print("❌ 错误: 无法读取图片文件")
            print("   可能的原因:")
            print("   - 文件格式不支持")
            print("   - 文件已损坏")
            print("   - 文件路径包含特殊字符")
            return False
        
        # 检查图片属性
        height, width = image.shape[:2]
        channels = image.shape[2] if len(image.shape) == 3 else 1
        
        print(f"📐 图片信息:")
        print(f"   尺寸: {width} x {height}")
        print(f"   通道数: {channels}")
        print(f"   数据类型: {image.dtype}")
        
        # 检查图片是否过小
        if width < 50 or height < 50:
            print("⚠️  警告: 图片尺寸较小，可能影响识别效果")
        
        # 尝试直接识别
        print("\n🎯 尝试直接识别...")
        results = scanner._decode_barcodes(image)
        
        if results:
            print(f"✅ 直接识别成功! 找到 {len(results)} 个码:")
            for i, result in enumerate(results, 1):
                print(f"   📱 码 {i}:")
                print(f"      内容: {result['data']}")
                print(f"      类型: {result['type']}")
                print(f"      位置: {result['position']}")
            return True
        
        print("   未能直接识别，尝试图像增强...")
        
        # 尝试各种增强方法
        enhancement_methods = [
            ("🔧 锐化处理", lambda img: processor.sharpen_image(img)),
            ("🌟 对比度增强", lambda img: processor.enhance_contrast(img, 1.5)),
            ("💡 亮度增强", lambda img: processor.enhance_brightness(img, 1.3)),
            ("🧹 去噪处理", lambda img: processor.remove_noise(img)),
            ("⚡ 自动增强", lambda img: processor.auto_enhance_for_barcode(img)),
        ]
        
        for method_name, enhance_func in enhancement_methods:
            print(f"\n{method_name}...")
            
            try:
                enhanced_image = enhance_func(image)
                
                # 检查增强后的图像
                if enhanced_image is None or enhanced_image.size == 0:
                    print(f"   ❌ {method_name}失败: 增强后图像为空")
                    continue
                
                # 如果是灰度图像，需要转换为BGR用于某些处理
                if len(enhanced_image.shape) == 2:
                    # 对于灰度图像，直接使用pyzbar
                    from pyzbar import pyzbar
                    barcodes = pyzbar.decode(enhanced_image)
                    
                    if barcodes:
                        print(f"   ✅ {method_name}识别成功!")
                        for i, barcode in enumerate(barcodes, 1):
                            barcode_data = barcode.data.decode('utf-8')
                            barcode_type = barcode.type
                            print(f"      📱 码 {i}: {barcode_data} (类型: {barcode_type})")
                        return True
                    else:
                        print(f"   ❌ {method_name}识别失败")
                else:
                    # 彩色图像使用原有方法
                    results = scanner._decode_barcodes(enhanced_image)
                    
                    if results:
                        print(f"   ✅ {method_name}识别成功!")
                        for i, result in enumerate(results, 1):
                            print(f"      📱 码 {i}: {result['data']} (类型: {result['type']})")
                        return True
                    else:
                        print(f"   ❌ {method_name}识别失败")
                        
            except Exception as e:
                print(f"   ❌ {method_name}处理出错: {str(e)}")
                continue
        
        # 尝试调整图片大小
        print(f"\n📏 尝试调整图片大小...")
        try:
            scales = [0.5, 1.5, 2.0, 0.8, 1.2]
            for scale in scales:
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                print(f"   🔄 尝试缩放 {scale}x ({new_width}x{new_height})...")
                
                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                enhanced = processor.auto_enhance_for_barcode(resized)
                
                # 直接使用pyzbar处理灰度图像
                from pyzbar import pyzbar
                barcodes = pyzbar.decode(enhanced)
                
                if barcodes:
                    print(f"   ✅ 缩放{scale}x识别成功!")
                    for i, barcode in enumerate(barcodes, 1):
                        barcode_data = barcode.data.decode('utf-8')
                        barcode_type = barcode.type
                        print(f"      📱 码 {i}: {barcode_data} (类型: {barcode_type})")
                    return True
                    
        except Exception as e:
            print(f"   ❌ 缩放处理出错: {str(e)}")
        
        print("\n❌ 所有方法都无法识别此图片中的二维码/条形码")
        print("\n💡 建议:")
        print("   - 确保图片清晰度足够")
        print("   - 检查二维码/条形码是否完整")
        print("   - 尝试调整光照条件重新拍摄")
        print("   - 确保二维码/条形码没有被遮挡")
        
        return False
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        print(f"   错误类型: {type(e).__name__}")
        return False


def main():
    """主函数"""
    print("🔲 安全的二维码/条形码识别测试")
    print("修复了OpenCV错误问题")
    print("=" * 60)
    
    # 测试图片路径
    image_path = "A44D407E-7666-4D26-90F1-D67E70FDEC4B-60305-000011.jpg"
    
    success = safe_test_image(image_path)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试完成 - 识别成功!")
    else:
        print("❌ 测试完成 - 识别失败")
        print("\n🔧 故障排除:")
        print("   1. 检查图片文件是否存在且完整")
        print("   2. 确保图片格式被支持 (jpg, png, bmp等)")
        print("   3. 检查二维码/条形码是否清晰可见")
        print("   4. 尝试使用其他图片测试")
    
    print(f"\n📝 要使用GUI界面测试，请运行: python main.py")


if __name__ == "__main__":
    main()
