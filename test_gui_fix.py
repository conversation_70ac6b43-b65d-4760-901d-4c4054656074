#!/usr/bin/env python3
"""
测试GUI修复是否成功
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gui_components():
    """测试GUI组件是否正常工作"""
    print("🔧 测试GUI组件修复...")
    
    try:
        # 测试导入
        from modules.gui import BarcodeGUI
        from modules.barcode_scanner import BarcodeScanner
        from modules.database import DeviceDatabase
        from modules.image_processor import ImageProcessor
        
        print("✅ 所有模块导入成功")
        
        # 测试组件初始化
        scanner = BarcodeScanner()
        db = DeviceDatabase()
        processor = ImageProcessor()
        
        print("✅ 核心组件初始化成功")
        
        # 测试图像处理的安全性
        import cv2
        import numpy as np
        
        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image.fill(128)
        
        # 测试处理方法
        enhanced = processor.auto_enhance_for_barcode(test_image)
        print(f"✅ 图像处理测试成功: {enhanced.shape}")
        
        # 测试扫描方法
        results = scanner._decode_barcodes(test_image)
        print(f"✅ 扫描方法测试成功: {len(results)} 个结果")
        
        print("\n🎉 所有组件测试通过！GUI应该可以正常工作了。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("GUI修复验证测试")
    print("=" * 50)
    
    success = test_gui_components()
    
    if success:
        print("\n✅ 修复验证成功！")
        print("现在可以安全地运行GUI界面:")
        print("  python main.py")
    else:
        print("\n❌ 修复验证失败，需要进一步检查")
    
    return success


if __name__ == "__main__":
    main()
