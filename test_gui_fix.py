#!/usr/bin/env python3
"""
测试GUI修复是否成功
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gui_fix():
    """测试GUI修复"""
    print("🔧 测试GUI修复...")

    try:
        # 测试导入
        from modules.gui import BarcodeGUI
        print("✅ GUI模块导入成功")

        # 检查是否添加了original_image属性
        gui = BarcodeGUI()

        # 检查属性
        if hasattr(gui, 'original_image'):
            print("✅ 已添加original_image属性")
        else:
            print("❌ 缺少original_image属性")
            return False

        print("\n🎉 GUI修复测试通过！")
        print("\n📝 修复内容:")
        print("   🔄 添加了original_image属性保存原始图像")
        print("   🔪 锐化增强现在直接作用于原始图像")
        print("   📷 摄像头图像也会保存原始副本")

        print("\n💡 现在的工作流程:")
        print("   1. 选择图片 → 保存原始图像")
        print("   2. 自动增强 → 作用于当前图像")
        print("   3. 锐化增强 → 始终作用于原始图像")
        print("   4. 扫描条形码 → 识别当前图像")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🛠️  GUI修复验证")
    print("=" * 30)

    success = test_gui_fix()

    if success:
        print("\n✅ 修复验证成功！")
        print("\n🚀 现在重新测试您的qq.png:")
        print("   python main.py")
        print("\n📋 新的操作步骤:")
        print("   1. 选择 qq.png 图片")
        print("   2. 直接点击 '锐化增强' 按钮")
        print("   3. 点击 '扫描条形码' 按钮")
        print("   4. 应该能成功识别: 03012201000224070030621")

        print("\n⚠️  重要提示:")
        print("   不要先点击'自动增强'再点击'锐化增强'")
        print("   直接使用'锐化增强'效果最好")
    else:
        print("\n❌ 修复验证失败")


if __name__ == "__main__":
    main()
