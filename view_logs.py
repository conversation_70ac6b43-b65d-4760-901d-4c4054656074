#!/usr/bin/env python3
"""
日志查看工具
实时查看系统操作日志
"""

import os
import time
import sys
from datetime import datetime


def tail_log_file(log_file="barcode_system.log", lines=20):
    """实时查看日志文件"""
    print(f"📋 查看日志文件: {log_file}")
    print("=" * 60)
    
    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        print("请先运行系统生成日志文件")
        return
    
    try:
        # 显示最近的日志
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            
            print(f"📖 显示最近 {len(recent_lines)} 行日志:")
            print("-" * 60)
            
            for line in recent_lines:
                line = line.strip()
                if line:
                    # 高亮重要操作
                    if "用户点击了" in line:
                        print(f"🖱️  {line}")
                    elif "图像增强" in line:
                        print(f"🔧 {line}")
                    elif "扫描条形码" in line:
                        print(f"🔍 {line}")
                    elif "识别到条形码" in line:
                        print(f"✅ {line}")
                    elif "ERROR" in line:
                        print(f"❌ {line}")
                    elif "WARNING" in line:
                        print(f"⚠️  {line}")
                    else:
                        print(f"   {line}")
            
            print("-" * 60)
            
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")


def watch_log_file(log_file="barcode_system.log"):
    """实时监控日志文件变化"""
    print(f"👀 实时监控日志文件: {log_file}")
    print("按 Ctrl+C 停止监控")
    print("=" * 60)
    
    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        print("等待日志文件创建...")
        
        # 等待文件创建
        while not os.path.exists(log_file):
            time.sleep(1)
        
        print(f"✅ 日志文件已创建: {log_file}")
    
    try:
        # 获取文件初始大小
        with open(log_file, 'r', encoding='utf-8') as f:
            f.seek(0, 2)  # 移动到文件末尾
            last_position = f.tell()
        
        print("开始监控...")
        
        while True:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    f.seek(last_position)
                    new_lines = f.readlines()
                    
                    if new_lines:
                        for line in new_lines:
                            line = line.strip()
                            if line:
                                timestamp = datetime.now().strftime("%H:%M:%S")
                                
                                # 高亮显示重要操作
                                if "用户点击了" in line:
                                    print(f"[{timestamp}] 🖱️  {line}")
                                elif "图像增强" in line and ("开始" in line or "完成" in line):
                                    print(f"[{timestamp}] 🔧 {line}")
                                elif "扫描条形码" in line:
                                    print(f"[{timestamp}] 🔍 {line}")
                                elif "识别到条形码" in line:
                                    print(f"[{timestamp}] ✅ {line}")
                                elif "ERROR" in line:
                                    print(f"[{timestamp}] ❌ {line}")
                                elif "WARNING" in line:
                                    print(f"[{timestamp}] ⚠️  {line}")
                                elif any(keyword in line for keyword in ["步骤", "处理", "算法", "转换"]):
                                    print(f"[{timestamp}] 🔄 {line}")
                    
                    last_position = f.tell()
                
                time.sleep(0.5)  # 每0.5秒检查一次
                
            except FileNotFoundError:
                print("⚠️  日志文件被删除，等待重新创建...")
                while not os.path.exists(log_file):
                    time.sleep(1)
                last_position = 0
                
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    except Exception as e:
        print(f"❌ 监控出错: {e}")


def analyze_log_file(log_file="barcode_system.log"):
    """分析日志文件，统计用户操作"""
    print(f"📊 分析日志文件: {log_file}")
    print("=" * 60)
    
    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        return
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 统计各种操作
        stats = {
            'select_image': 0,
            'enhance_image': 0,
            'scan_barcode': 0,
            'camera_operations': 0,
            'successful_scans': 0,
            'failed_scans': 0,
            'errors': 0,
            'warnings': 0
        }
        
        operations_log = []
        
        for line in lines:
            line = line.strip()
            
            if "用户点击了'选择图片'" in line:
                stats['select_image'] += 1
                operations_log.append("📁 选择图片")
                
            elif "用户点击了'图像增强'" in line:
                stats['enhance_image'] += 1
                operations_log.append("🔧 图像增强")
                
            elif "用户点击了'扫描条形码'" in line:
                stats['scan_barcode'] += 1
                operations_log.append("🔍 扫描条形码")
                
            elif "用户点击了摄像头" in line:
                stats['camera_operations'] += 1
                operations_log.append("📷 摄像头操作")
                
            elif "识别到条形码" in line and "个条形码" not in line:
                stats['successful_scans'] += 1
                
            elif "未识别到条形码" in line:
                stats['failed_scans'] += 1
                
            elif "ERROR" in line:
                stats['errors'] += 1
                
            elif "WARNING" in line:
                stats['warnings'] += 1
        
        # 显示统计结果
        print("📈 操作统计:")
        print(f"   📁 选择图片: {stats['select_image']} 次")
        print(f"   🔧 图像增强: {stats['enhance_image']} 次")
        print(f"   🔍 扫描条形码: {stats['scan_barcode']} 次")
        print(f"   📷 摄像头操作: {stats['camera_operations']} 次")
        print(f"   ✅ 成功识别: {stats['successful_scans']} 次")
        print(f"   ❌ 识别失败: {stats['failed_scans']} 次")
        print(f"   ⚠️  警告: {stats['warnings']} 次")
        print(f"   🚨 错误: {stats['errors']} 次")
        
        # 显示最近操作
        if operations_log:
            print(f"\n📋 最近操作 (最多显示10个):")
            recent_ops = operations_log[-10:]
            for i, op in enumerate(recent_ops, 1):
                print(f"   {i:2d}. {op}")
        
        # 分析使用模式
        print(f"\n🔍 使用分析:")
        if stats['enhance_image'] > 0 and stats['scan_barcode'] > 0:
            enhance_ratio = stats['enhance_image'] / stats['scan_barcode']
            if enhance_ratio >= 0.8:
                print("   ✅ 用户经常使用图像增强功能 (推荐做法)")
            elif enhance_ratio >= 0.5:
                print("   🔄 用户有时使用图像增强功能")
            else:
                print("   ⚠️  用户很少使用图像增强功能 (建议多使用)")
        
        if stats['successful_scans'] > 0 and stats['failed_scans'] > 0:
            success_ratio = stats['successful_scans'] / (stats['successful_scans'] + stats['failed_scans'])
            print(f"   📊 识别成功率: {success_ratio:.1%}")
        
    except Exception as e:
        print(f"❌ 分析日志文件失败: {e}")


def main():
    """主函数"""
    print("📋 条形码系统日志查看工具")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "watch":
            watch_log_file()
        elif command == "analyze":
            analyze_log_file()
        elif command == "tail":
            lines = int(sys.argv[2]) if len(sys.argv) > 2 else 20
            tail_log_file(lines=lines)
        else:
            print("❌ 未知命令")
            print_usage()
    else:
        print_usage()


def print_usage():
    """显示使用说明"""
    print("使用方法:")
    print("  python view_logs.py tail [行数]    # 查看最近的日志 (默认20行)")
    print("  python view_logs.py watch         # 实时监控日志")
    print("  python view_logs.py analyze       # 分析日志统计")
    print()
    print("示例:")
    print("  python view_logs.py tail 50       # 查看最近50行日志")
    print("  python view_logs.py watch         # 实时监控")
    print("  python view_logs.py analyze       # 统计分析")


if __name__ == "__main__":
    main()
