#!/usr/bin/env python3
"""
调试GUI处理和脚本处理的差异
"""

import sys
import os
import cv2

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner
from modules.image_processor import ImageProcessor


def debug_processing_difference():
    """调试处理差异"""
    print("🔍 调试GUI处理和脚本处理的差异")
    print("=" * 50)
    
    # 查找图片
    image_path = "C:/Users/<USER>/Desktop/qq.png"
    if not os.path.exists(image_path):
        print(f"❌ 图片不存在: {image_path}")
        return False
    
    scanner = BarcodeScanner()
    processor = ImageProcessor()
    
    try:
        # 读取原始图片
        original_image = cv2.imread(image_path)
        print(f"📁 原始图片尺寸: {original_image.shape}")
        
        # 方法1: 直接锐化 (脚本中成功的方法)
        print("\n🔬 方法1: 直接锐化 (脚本中成功的方法)")
        sharpened_direct = processor.sharpen_image(original_image)
        print(f"   锐化后尺寸: {sharpened_direct.shape}")
        
        results1 = scanner._decode_barcodes(sharpened_direct)
        if results1:
            print(f"   ✅ 方法1成功: {results1[0]['data']}")
        else:
            print(f"   ❌ 方法1失败")
        
        # 方法2: 模拟GUI的处理流程
        print("\n🔬 方法2: 模拟GUI的锐化处理流程")
        
        # 先进行自动增强 (GUI中用户先点了自动增强)
        auto_enhanced = processor.auto_enhance_for_barcode(original_image)
        print(f"   自动增强后尺寸: {auto_enhanced.shape}")
        
        # 将灰度图转换为BGR (模拟GUI中的转换)
        if len(auto_enhanced.shape) == 2:
            bgr_converted = cv2.cvtColor(auto_enhanced, cv2.COLOR_GRAY2BGR)
            print(f"   转换为BGR后尺寸: {bgr_converted.shape}")
        else:
            bgr_converted = auto_enhanced
        
        # 再进行锐化 (用户点击锐化增强)
        sharpened_gui = processor.sharpen_image(bgr_converted)
        print(f"   GUI锐化后尺寸: {sharpened_gui.shape}")
        
        results2 = scanner._decode_barcodes(sharpened_gui)
        if results2:
            print(f"   ✅ 方法2成功: {results2[0]['data']}")
        else:
            print(f"   ❌ 方法2失败")
        
        # 方法3: 重新加载原图进行锐化
        print("\n🔬 方法3: 重新加载原图进行锐化")
        fresh_image = cv2.imread(image_path)
        fresh_sharpened = processor.sharpen_image(fresh_image)
        
        results3 = scanner._decode_barcodes(fresh_sharpened)
        if results3:
            print(f"   ✅ 方法3成功: {results3[0]['data']}")
        else:
            print(f"   ❌ 方法3失败")
        
        # 方法4: 去噪+锐化 (分析中成功的另一种方法)
        print("\n🔬 方法4: 去噪+锐化")
        denoised = processor.remove_noise(original_image)
        denoised_sharpened = processor.sharpen_image(denoised)
        
        results4 = scanner._decode_barcodes(denoised_sharpened)
        if results4:
            print(f"   ✅ 方法4成功: {results4[0]['data']}")
        else:
            print(f"   ❌ 方法4失败")
        
        # 分析问题
        print(f"\n📊 分析结果:")
        success_methods = []
        if results1: success_methods.append("直接锐化")
        if results2: success_methods.append("GUI流程锐化")
        if results3: success_methods.append("重新加载锐化")
        if results4: success_methods.append("去噪+锐化")
        
        if success_methods:
            print(f"   ✅ 成功的方法: {', '.join(success_methods)}")
        else:
            print(f"   ❌ 所有方法都失败了")
        
        # 如果GUI流程失败但其他成功，说明问题在GUI处理流程
        if results1 and not results2:
            print(f"\n🔍 问题诊断:")
            print(f"   GUI中的处理流程可能有问题")
            print(f"   建议修改GUI让用户能直接对原图进行锐化")
        
        return len(success_methods) > 0
        
    except Exception as e:
        print(f"❌ 调试出错: {e}")
        return False


def main():
    """主函数"""
    success = debug_processing_difference()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎯 找到了问题所在！")
        print("\n💡 解决方案:")
        print("   需要修改GUI，让锐化增强直接作用于原图")
        print("   而不是作用于已经增强过的图像")
    else:
        print("😔 调试未能找到解决方案")
        print("   可能需要检查图片本身或环境问题")


if __name__ == "__main__":
    main()
