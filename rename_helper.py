#!/usr/bin/env python3
"""
文件重命名助手
帮助处理中文文件名问题
"""

import os
import shutil
from datetime import datetime


def rename_chinese_files():
    """重命名包含中文的图片文件"""
    print("🔧 文件重命名助手")
    print("=" * 40)
    
    # 常见的中文文件名模式
    chinese_patterns = [
        "QQ截图",
        "微信图片", 
        "屏幕截图",
        "图片",
        "照片"
    ]
    
    # 扫描当前目录和桌面
    search_paths = [
        ".",
        "C:/Users/<USER>/Desktop"
    ]
    
    renamed_files = []
    
    for search_path in search_paths:
        if not os.path.exists(search_path):
            continue
            
        print(f"\n📁 扫描目录: {search_path}")
        
        try:
            files = os.listdir(search_path)
        except PermissionError:
            print(f"   ⚠️  无权限访问目录")
            continue
        
        for filename in files:
            # 检查是否是图片文件
            if not filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                continue
            
            # 检查是否包含中文
            has_chinese = any(pattern in filename for pattern in chinese_patterns)
            has_chinese = has_chinese or any('\u4e00' <= char <= '\u9fff' for char in filename)
            
            if has_chinese:
                old_path = os.path.join(search_path, filename)
                
                # 生成新的英文文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                extension = os.path.splitext(filename)[1]
                new_filename = f"qr_image_{timestamp}{extension}"
                new_path = os.path.join(search_path, new_filename)
                
                try:
                    # 复制文件而不是移动，保留原文件
                    shutil.copy2(old_path, new_path)
                    print(f"   ✅ 已复制: {filename}")
                    print(f"      → {new_filename}")
                    renamed_files.append((old_path, new_path))
                    
                except Exception as e:
                    print(f"   ❌ 复制失败: {filename} - {e}")
    
    if renamed_files:
        print(f"\n🎉 成功处理 {len(renamed_files)} 个文件")
        print("\n📋 处理结果:")
        for old_path, new_path in renamed_files:
            print(f"   原文件: {os.path.basename(old_path)}")
            print(f"   新文件: {os.path.basename(new_path)}")
            print(f"   路径: {os.path.dirname(new_path)}")
            print()
        
        print("💡 建议:")
        print("   1. 使用新的英文文件名进行识别")
        print("   2. 原文件已保留，可以安全删除")
        print("   3. 在GUI中选择新的英文文件名")
        
    else:
        print("\n📝 未找到需要处理的中文文件名图片")
    
    return renamed_files


def main():
    """主函数"""
    print("文件名处理工具")
    print("解决中文文件名导致的OpenCV读取问题")
    print()
    
    renamed_files = rename_chinese_files()
    
    if renamed_files:
        print(f"\n🚀 现在可以在GUI中使用新的英文文件名了！")
        print("   启动GUI: python main.py")
    
    return len(renamed_files) > 0


if __name__ == "__main__":
    main()
