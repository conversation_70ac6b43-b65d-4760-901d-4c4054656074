#!/usr/bin/env python3
"""
测试新增的GUI功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_gui_features():
    """测试新增的GUI功能"""
    print("🔧 测试新增的GUI功能...")
    
    try:
        # 测试导入
        from modules.gui import BarcodeGUI
        print("✅ GUI模块导入成功")
        
        # 测试图像处理器的锐化功能
        from modules.image_processor import ImageProcessor
        import cv2
        import numpy as np
        
        processor = ImageProcessor()
        
        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image.fill(128)
        
        # 测试锐化功能
        sharpened = processor.sharpen_image(test_image)
        print(f"✅ 锐化功能测试成功: {sharpened.shape}")
        
        print("\n🎉 新功能测试通过！")
        print("\n📝 新增功能:")
        print("   🔪 锐化增强按钮 - 专门针对您的qq.png图片优化")
        print("   🔧 自动增强按钮 - 原有的综合增强功能")
        
        print("\n💡 使用建议:")
        print("   对于您的qq.png图片，建议使用'锐化增强'按钮")
        print("   因为分析显示锐化处理对这张图片效果最好")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🆕 新GUI功能测试")
    print("=" * 30)
    
    success = test_new_gui_features()
    
    if success:
        print("\n✅ 测试成功！")
        print("\n🚀 现在启动GUI试试新功能:")
        print("   python main.py")
        print("\n📋 操作步骤:")
        print("   1. 选择 qq.png 图片")
        print("   2. 点击 '锐化增强' 按钮 (新功能)")
        print("   3. 点击 '扫描条形码' 按钮")
        print("   4. 应该能成功识别到: 03012201000224070030621")
    else:
        print("\n❌ 测试失败")


if __name__ == "__main__":
    main()
