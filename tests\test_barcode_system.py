#!/usr/bin/env python3
"""
条形码识别系统测试模块
"""

import unittest
import sys
import os
import tempfile
import numpy as np
import cv2
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.barcode_scanner import BarcodeScanner
from modules.database import DeviceDatabase
from modules.image_processor import ImageProcessor


class TestBarcodeScanner(unittest.TestCase):
    """测试条形码扫描器"""
    
    def setUp(self):
        """测试前准备"""
        self.scanner = BarcodeScanner()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsInstance(self.scanner, BarcodeScanner)
        self.assertTrue(len(self.scanner.get_supported_formats()) > 0)
    
    def test_supported_formats(self):
        """测试支持的格式"""
        formats = self.scanner.get_supported_formats()
        expected_formats = ['CODE128', 'CODE39', 'EAN13', 'QRCODE']
        
        for fmt in expected_formats:
            self.assertIn(fmt, formats)
    
    def test_validate_barcode_data(self):
        """测试条形码数据验证"""
        # 测试有效数据
        self.assertTrue(self.scanner.validate_barcode_data("123456789"))
        self.assertTrue(self.scanner.validate_barcode_data("ABC123DEF"))
        
        # 测试无效数据
        self.assertFalse(self.scanner.validate_barcode_data(""))
        self.assertFalse(self.scanner.validate_barcode_data("12"))
        
        # 测试特定格式验证
        self.assertTrue(self.scanner.validate_barcode_data("1234567890123", "EAN13"))
        self.assertFalse(self.scanner.validate_barcode_data("123456789", "EAN13"))
    
    def test_scan_from_nonexistent_image(self):
        """测试扫描不存在的图片"""
        results = self.scanner.scan_from_image("nonexistent.jpg")
        self.assertEqual(results, [])


class TestDeviceDatabase(unittest.TestCase):
    """测试设备数据库"""
    
    def setUp(self):
        """测试前准备"""
        # 使用临时数据库文件
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DeviceDatabase(self.temp_db.name)
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时数据库文件
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_initialization(self):
        """测试数据库初始化"""
        self.assertIsInstance(self.db, DeviceDatabase)
        
        # 测试统计信息
        stats = self.db.get_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('total_devices', stats)
    
    def test_add_device(self):
        """测试添加设备"""
        device_info = {
            'sn_code': 'TEST001',
            'device_name': '测试设备',
            'device_type': '服务器',
            'manufacturer': '测试厂商',
            'model': 'TEST-MODEL-001'
        }
        
        # 添加设备
        result = self.db.add_device(device_info)
        self.assertTrue(result)
        
        # 验证设备已添加
        device = self.db.get_device('TEST001')
        self.assertIsNotNone(device)
        self.assertEqual(device['sn_code'], 'TEST001')
        self.assertEqual(device['device_name'], '测试设备')
    
    def test_duplicate_device(self):
        """测试重复设备"""
        device_info = {
            'sn_code': 'TEST002',
            'device_name': '测试设备2'
        }
        
        # 第一次添加应该成功
        result1 = self.db.add_device(device_info)
        self.assertTrue(result1)
        
        # 第二次添加相同SN码应该失败
        result2 = self.db.add_device(device_info)
        self.assertFalse(result2)
    
    def test_update_device(self):
        """测试更新设备"""
        # 先添加设备
        device_info = {
            'sn_code': 'TEST003',
            'device_name': '原始名称'
        }
        self.db.add_device(device_info)
        
        # 更新设备
        updates = {'device_name': '更新后名称', 'location': '新位置'}
        result = self.db.update_device('TEST003', updates)
        self.assertTrue(result)
        
        # 验证更新
        device = self.db.get_device('TEST003')
        self.assertEqual(device['device_name'], '更新后名称')
        self.assertEqual(device['location'], '新位置')
    
    def test_delete_device(self):
        """测试删除设备"""
        # 先添加设备
        device_info = {
            'sn_code': 'TEST004',
            'device_name': '待删除设备'
        }
        self.db.add_device(device_info)
        
        # 删除设备
        result = self.db.delete_device('TEST004')
        self.assertTrue(result)
        
        # 验证设备已删除
        device = self.db.get_device('TEST004')
        self.assertIsNone(device)
    
    def test_get_all_devices(self):
        """测试获取所有设备"""
        # 添加多个设备
        for i in range(3):
            device_info = {
                'sn_code': f'TEST00{i+5}',
                'device_name': f'测试设备{i+5}'
            }
            self.db.add_device(device_info)
        
        # 获取所有设备
        devices = self.db.get_all_devices()
        self.assertGreaterEqual(len(devices), 3)
    
    def test_scan_history(self):
        """测试扫描历史"""
        scan_info = {
            'sn_code': 'TEST006',
            'scan_result': '扫描成功',
            'operator': '测试用户'
        }
        
        # 添加扫描历史
        result = self.db.add_scan_history(scan_info)
        self.assertTrue(result)
        
        # 获取扫描历史
        history = self.db.get_scan_history('TEST006')
        self.assertGreater(len(history), 0)
        self.assertEqual(history[0]['sn_code'], 'TEST006')


class TestImageProcessor(unittest.TestCase):
    """测试图像处理器"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = ImageProcessor()
        
        # 创建测试图像
        self.test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        self.test_image.fill(128)  # 灰色背景
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsInstance(self.processor, ImageProcessor)
    
    def test_preprocess_for_barcode(self):
        """测试条形码预处理"""
        processed = self.processor.preprocess_for_barcode(self.test_image)
        
        # 检查输出是否为灰度图像
        self.assertEqual(len(processed.shape), 2)
        self.assertEqual(processed.dtype, np.uint8)
    
    def test_enhance_contrast(self):
        """测试对比度增强"""
        enhanced = self.processor.enhance_contrast(self.test_image, 1.5)
        
        # 检查输出形状
        self.assertEqual(enhanced.shape, self.test_image.shape)
        self.assertEqual(enhanced.dtype, self.test_image.dtype)
    
    def test_enhance_brightness(self):
        """测试亮度增强"""
        enhanced = self.processor.enhance_brightness(self.test_image, 1.2)
        
        # 检查输出形状
        self.assertEqual(enhanced.shape, self.test_image.shape)
        self.assertEqual(enhanced.dtype, self.test_image.dtype)
    
    def test_sharpen_image(self):
        """测试图像锐化"""
        sharpened = self.processor.sharpen_image(self.test_image)
        
        # 检查输出形状
        self.assertEqual(sharpened.shape, self.test_image.shape)
        self.assertEqual(sharpened.dtype, self.test_image.dtype)
    
    def test_remove_noise(self):
        """测试去噪"""
        denoised = self.processor.remove_noise(self.test_image)
        
        # 检查输出形状
        self.assertEqual(denoised.shape, self.test_image.shape)
        self.assertEqual(denoised.dtype, self.test_image.dtype)
    
    def test_rotate_image(self):
        """测试图像旋转"""
        rotated = self.processor.rotate_image(self.test_image, 45)
        
        # 检查输出形状
        self.assertEqual(rotated.shape, self.test_image.shape)
        self.assertEqual(rotated.dtype, self.test_image.dtype)
    
    def test_resize_image(self):
        """测试图像大小调整"""
        target_size = (150, 100)
        resized = self.processor.resize_image(self.test_image, target_size)
        
        # 检查输出大小
        self.assertEqual(resized.shape[:2], (100, 150))  # (height, width)
    
    def test_crop_roi(self):
        """测试ROI裁剪"""
        roi = (50, 50, 100, 80)  # x, y, width, height
        cropped = self.processor.crop_roi(self.test_image, roi)
        
        # 检查输出大小
        self.assertEqual(cropped.shape[:2], (80, 100))  # (height, width)
    
    def test_auto_enhance_for_barcode(self):
        """测试自动增强"""
        enhanced = self.processor.auto_enhance_for_barcode(self.test_image)
        
        # 检查输出是否为灰度图像
        self.assertEqual(len(enhanced.shape), 2)
        self.assertEqual(enhanced.dtype, np.uint8)
    
    def test_detect_barcode_region(self):
        """测试条形码区域检测"""
        region = self.processor.detect_barcode_region(self.test_image)
        
        # 对于纯色图像，可能检测不到条形码区域
        self.assertIsInstance(region, (tuple, type(None)))


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.scanner = BarcodeScanner()
        self.database = DeviceDatabase(self.temp_db.name)
        self.processor = ImageProcessor()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 创建测试图像
        test_image = np.zeros((200, 400, 3), dtype=np.uint8)
        test_image.fill(255)  # 白色背景
        
        # 2. 图像预处理
        processed = self.processor.auto_enhance_for_barcode(test_image)
        self.assertIsNotNone(processed)
        
        # 3. 条形码扫描（对于空白图像，应该返回空结果）
        results = self.scanner._decode_barcodes(test_image)
        self.assertIsInstance(results, list)
        
        # 4. 数据库操作
        device_info = {
            'sn_code': 'INTEGRATION_TEST_001',
            'device_name': '集成测试设备',
            'device_type': '测试设备'
        }
        
        # 添加设备
        add_result = self.database.add_device(device_info)
        self.assertTrue(add_result)
        
        # 查询设备
        device = self.database.get_device('INTEGRATION_TEST_001')
        self.assertIsNotNone(device)
        self.assertEqual(device['sn_code'], 'INTEGRATION_TEST_001')
        
        # 添加扫描历史
        scan_info = {
            'sn_code': 'INTEGRATION_TEST_001',
            'scan_result': '集成测试扫描',
            'operator': '测试系统'
        }
        
        history_result = self.database.add_scan_history(scan_info)
        self.assertTrue(history_result)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestBarcodeScanner,
        TestDeviceDatabase,
        TestImageProcessor,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("=" * 60)
    print("条形码识别系统测试")
    print("=" * 60)
    
    success = run_tests()
    
    if success:
        print("\n所有测试通过！")
        sys.exit(0)
    else:
        print("\n部分测试失败！")
        sys.exit(1)
