#!/usr/bin/env python3
"""
测试日志功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_logging():
    """测试日志功能"""
    print("🔧 测试日志功能...")
    
    try:
        # 导入模块触发日志初始化
        from modules.barcode_scanner import BarcodeScanner
        from modules.image_processor import ImageProcessor
        
        print("✅ 模块导入成功")
        
        # 创建组件
        scanner = BarcodeScanner()
        processor = ImageProcessor()
        
        print("✅ 组件创建成功")
        
        # 检查日志文件
        log_file = "barcode_system.log"
        if os.path.exists(log_file):
            print(f"✅ 日志文件存在: {log_file}")
            
            # 读取最后几行
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"📋 日志文件包含 {len(lines)} 行")
                    print("最近的日志:")
                    for line in lines[-3:]:
                        print(f"   {line.strip()}")
                else:
                    print("⚠️  日志文件为空")
        else:
            print("❌ 日志文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("📋 日志功能测试")
    print("=" * 30)
    
    success = test_logging()
    
    if success:
        print("\n✅ 日志功能正常")
        print("\n📝 使用说明:")
        print("1. 启动GUI: python main.py")
        print("2. 查看日志: python view_logs.py tail")
        print("3. 实时监控: python view_logs.py watch")
        print("4. 统计分析: python view_logs.py analyze")
    else:
        print("\n❌ 日志功能异常")


if __name__ == "__main__":
    main()
