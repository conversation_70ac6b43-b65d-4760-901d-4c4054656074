# 条形码识别入库管理系统 - GUI使用指南

## 🚀 启动系统

```bash
python main.py
```

## 📱 二维码/条形码识别步骤

### 方法一：图片文件识别（推荐）

1. **选择图片**
   - 点击 `选择图片` 按钮
   - 选择包含二维码/条形码的图片文件
   - 支持格式：JPG, PNG, BMP, TIFF

2. **图像增强**（重要！）
   - 点击 `图像增强` 按钮
   - 系统会自动优化图片质量
   - 特别适用于模糊或光线不佳的图片

3. **扫描识别**
   - 点击 `扫描条形码` 按钮
   - 系统会显示识别结果
   - 成功识别后，SN码会自动填入表单

### 方法二：摄像头实时扫描

1. **启动摄像头**
   - 点击 `启动摄像头` 按钮
   - 将二维码/条形码对准摄像头

2. **扫描操作**
   - 按 `S` 键进行扫描
   - 按 `Q` 键退出摄像头模式

## 📊 设备信息管理

### 添加设备
1. 识别二维码后，SN码会自动填入
2. 手动填写其他设备信息：
   - 设备名称
   - 设备类型
   - 制造商
   - 型号
   - 位置
   - 备注
3. 点击 `添加设备` 按钮

### 管理设备
- **查看设备**：在设备列表中双击查看详情
- **更新设备**：修改信息后点击 `更新设备`
- **删除设备**：选择设备后点击 `删除设备`
- **刷新列表**：点击 `刷新列表` 更新显示

## 🔧 故障排除

### 图片识别失败
1. **先尝试图像增强**
   - 点击 `图像增强` 按钮
   - 再点击 `扫描条形码`

2. **检查图片质量**
   - 确保二维码/条形码清晰可见
   - 避免反光或阴影
   - 确保图片完整

3. **支持的格式**
   - 二维码：QR码、DataMatrix、PDF417
   - 条形码：Code128、Code39、EAN13等

### 摄像头问题
- 确保摄像头未被其他程序占用
- 检查摄像头权限设置
- 尝试重启程序

### 文件路径问题
- 避免文件路径包含中文字符
- 确保文件名不包含特殊符号
- 建议将图片放在英文路径下

## 💡 使用技巧

### 最佳识别效果
1. **图片质量**
   - 分辨率不低于 300x300 像素
   - 二维码/条形码占图片面积的 20-80%
   - 避免过度压缩的图片

2. **光照条件**
   - 均匀光照，避免强烈阴影
   - 避免反光
   - 室内自然光或柔和灯光最佳

3. **拍摄角度**
   - 正面拍摄，避免倾斜
   - 保持适当距离
   - 确保二维码/条形码完整在画面内

### 批量处理建议
1. 对于相似质量的图片，都建议先进行图像增强
2. 建立统一的命名规则
3. 定期备份数据库文件

## 📋 二维码数据格式建议

### 简单格式
```
SN:SERVER001
```

### JSON格式（推荐）
```json
{
  "sn": "SERVER001",
  "name": "生产服务器01",
  "type": "服务器",
  "manufacturer": "Dell",
  "model": "PowerEdge R740",
  "location": "机房A-01",
  "ip": "*************"
}
```

### URL格式
```
https://device-mgmt.company.com/device/SERVER001
```

## 🗃️ 数据管理

### 数据库位置
- 数据库文件：`data/devices.db`
- 日志文件：`barcode_system.log`

### 备份建议
- 定期备份 `data/devices.db` 文件
- 重要操作前建议备份

### 数据导出
- 可通过数据库工具查看SQLite数据
- 支持标准SQL查询

## 🆘 常见错误解决

### "OpenCV错误"
- 已修复，确保使用最新版本的程序

### "图片文件不存在"
- 检查文件路径是否正确
- 确保文件未被移动或删除

### "未识别到条形码"
- 尝试图像增强功能
- 检查图片质量和格式
- 确认是支持的条形码类型

### "设备SN码已存在"
- 该设备已在数据库中
- 可以选择更新现有设备信息

## 📞 技术支持

如遇到问题：
1. 查看日志文件 `barcode_system.log`
2. 尝试重启程序
3. 检查依赖包是否正确安装
4. 参考README.md文档

---

**祝您使用愉快！** 🎉
