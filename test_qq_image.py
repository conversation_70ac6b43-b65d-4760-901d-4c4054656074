#!/usr/bin/env python3
"""
测试qq.png图片的识别效果
"""

import sys
import os
import cv2

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.barcode_scanner import BarcodeScanner
from modules.image_processor import ImageProcessor


def test_qq_image():
    """测试qq.png图片"""
    print("🔍 测试 qq.png 图片识别")
    print("=" * 40)
    
    # 可能的文件路径
    possible_paths = [
        "qq.png",
        "C:/Users/<USER>/Desktop/qq.png",
        "../qq.png"
    ]
    
    image_path = None
    for path in possible_paths:
        if os.path.exists(path):
            image_path = path
            break
    
    if not image_path:
        print("❌ 未找到 qq.png 文件")
        print("请确保文件在以下位置之一:")
        for path in possible_paths:
            print(f"   - {path}")
        return False
    
    print(f"📁 找到图片: {image_path}")
    
    # 初始化组件
    scanner = BarcodeScanner()
    processor = ImageProcessor()
    
    try:
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print("❌ 无法读取图片")
            return False
        
        height, width = image.shape[:2]
        print(f"📐 图片尺寸: {width} x {height}")
        
        # 1. 直接识别
        print("\n1️⃣ 直接识别...")
        results = scanner._decode_barcodes(image)
        if results:
            print("✅ 直接识别成功!")
            for i, result in enumerate(results, 1):
                print(f"   码 {i}: {result['data']} (类型: {result['type']})")
            return True
        else:
            print("   未能直接识别")
        
        # 2. 锐化处理
        print("\n2️⃣ 锐化处理...")
        sharpened = processor.sharpen_image(image)
        results = scanner._decode_barcodes(sharpened)
        if results:
            print("✅ 锐化处理识别成功!")
            for i, result in enumerate(results, 1):
                print(f"   码 {i}: {result['data']} (类型: {result['type']})")
            return True
        else:
            print("   锐化处理识别失败")
        
        # 3. 自动增强
        print("\n3️⃣ 自动增强...")
        enhanced = processor.auto_enhance_for_barcode(image)
        
        # 直接使用pyzbar处理灰度图像
        from pyzbar import pyzbar
        barcodes = pyzbar.decode(enhanced)
        if barcodes:
            print("✅ 自动增强识别成功!")
            for i, barcode in enumerate(barcodes, 1):
                barcode_data = barcode.data.decode('utf-8')
                barcode_type = barcode.type
                print(f"   码 {i}: {barcode_data} (类型: {barcode_type})")
            return True
        else:
            print("   自动增强识别失败")
        
        # 4. 对比度增强
        print("\n4️⃣ 对比度增强...")
        contrast_enhanced = processor.enhance_contrast(image, 2.0)
        results = scanner._decode_barcodes(contrast_enhanced)
        if results:
            print("✅ 对比度增强识别成功!")
            for i, result in enumerate(results, 1):
                print(f"   码 {i}: {result['data']} (类型: {result['type']})")
            return True
        else:
            print("   对比度增强识别失败")
        
        # 5. 尺寸调整
        print("\n5️⃣ 尺寸调整...")
        scales = [0.5, 1.5, 2.0]
        for scale in scales:
            new_width = int(width * scale)
            new_height = int(height * scale)
            resized = cv2.resize(image, (new_width, new_height))
            enhanced = processor.auto_enhance_for_barcode(resized)
            
            barcodes = pyzbar.decode(enhanced)
            if barcodes:
                print(f"✅ 缩放{scale}x识别成功!")
                for i, barcode in enumerate(barcodes, 1):
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type
                    print(f"   码 {i}: {barcode_data} (类型: {barcode_type})")
                return True
        
        print("   所有缩放尝试都失败")
        
        print("\n❌ 所有识别方法都失败了")
        print("\n💡 可能的原因:")
        print("   - 二维码/条形码太小或模糊")
        print("   - 图片质量不够清晰")
        print("   - 二维码/条形码被部分遮挡")
        print("   - 光照条件不佳")
        
        print("\n🔧 建议:")
        print("   - 尝试更清晰的图片")
        print("   - 确保二维码/条形码完整可见")
        print("   - 在GUI中手动尝试'图像增强'功能")
        
        return False
        
    except Exception as e:
        print(f"❌ 处理出错: {e}")
        return False


def main():
    """主函数"""
    success = test_qq_image()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 识别成功！可以在GUI中使用此图片")
    else:
        print("😔 识别失败，建议尝试其他图片或在GUI中使用图像增强功能")
    
    print("\n📝 GUI使用提示:")
    print("   1. 启动GUI: python main.py")
    print("   2. 选择 qq.png 图片")
    print("   3. 点击'图像增强'按钮")
    print("   4. 点击'扫描条形码'按钮")


if __name__ == "__main__":
    main()
