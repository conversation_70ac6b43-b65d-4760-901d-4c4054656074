"""
数据库操作模块
管理设备信息的SQLite数据库操作
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DeviceDatabase:
    """设备数据库管理类"""
    
    def __init__(self, db_path: str = "data/devices.db"):
        """
        初始化数据库连接
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_database()
        logger.info(f"数据库初始化完成: {db_path}")
    
    def _ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建设备信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS devices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sn_code TEXT UNIQUE NOT NULL,
                        device_name TEXT,
                        device_type TEXT,
                        manufacturer TEXT,
                        model TEXT,
                        barcode_type TEXT,
                        scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        location TEXT,
                        status TEXT DEFAULT 'active',
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建扫描历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scan_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sn_code TEXT NOT NULL,
                        scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        scan_result TEXT,
                        image_path TEXT,
                        operator TEXT,
                        FOREIGN KEY (sn_code) REFERENCES devices (sn_code)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sn_code ON devices (sn_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scan_time ON scan_history (scan_time)')
                
                conn.commit()
                logger.info("数据库表结构创建完成")
                
        except sqlite3.Error as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    def add_device(self, device_info: Dict) -> bool:
        """
        添加设备信息
        
        Args:
            device_info: 设备信息字典
            
        Returns:
            操作是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查SN码是否已存在
                cursor.execute('SELECT id FROM devices WHERE sn_code = ?', 
                             (device_info['sn_code'],))
                if cursor.fetchone():
                    logger.warning(f"设备SN码已存在: {device_info['sn_code']}")
                    return False
                
                # 插入设备信息
                cursor.execute('''
                    INSERT INTO devices (
                        sn_code, device_name, device_type, manufacturer, 
                        model, barcode_type, location, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    device_info.get('sn_code'),
                    device_info.get('device_name', ''),
                    device_info.get('device_type', ''),
                    device_info.get('manufacturer', ''),
                    device_info.get('model', ''),
                    device_info.get('barcode_type', ''),
                    device_info.get('location', ''),
                    device_info.get('notes', '')
                ))
                
                conn.commit()
                logger.info(f"设备添加成功: {device_info['sn_code']}")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"添加设备失败: {str(e)}")
            return False
    
    def get_device(self, sn_code: str) -> Optional[Dict]:
        """
        根据SN码获取设备信息
        
        Args:
            sn_code: 设备SN码
            
        Returns:
            设备信息字典或None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM devices WHERE sn_code = ?', (sn_code,))
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return None
                
        except sqlite3.Error as e:
            logger.error(f"查询设备失败: {str(e)}")
            return None
    
    def update_device(self, sn_code: str, updates: Dict) -> bool:
        """
        更新设备信息
        
        Args:
            sn_code: 设备SN码
            updates: 更新的字段字典
            
        Returns:
            操作是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 构建更新SQL
                set_clause = ', '.join([f"{key} = ?" for key in updates.keys()])
                values = list(updates.values()) + [datetime.now(), sn_code]
                
                cursor.execute(f'''
                    UPDATE devices 
                    SET {set_clause}, updated_at = ?
                    WHERE sn_code = ?
                ''', values)
                
                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"设备更新成功: {sn_code}")
                    return True
                else:
                    logger.warning(f"未找到要更新的设备: {sn_code}")
                    return False
                    
        except sqlite3.Error as e:
            logger.error(f"更新设备失败: {str(e)}")
            return False
    
    def delete_device(self, sn_code: str) -> bool:
        """
        删除设备信息
        
        Args:
            sn_code: 设备SN码
            
        Returns:
            操作是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('DELETE FROM devices WHERE sn_code = ?', (sn_code,))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"设备删除成功: {sn_code}")
                    return True
                else:
                    logger.warning(f"未找到要删除的设备: {sn_code}")
                    return False
                    
        except sqlite3.Error as e:
            logger.error(f"删除设备失败: {str(e)}")
            return False
    
    def get_all_devices(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """
        获取所有设备信息
        
        Args:
            limit: 限制返回数量
            offset: 偏移量
            
        Returns:
            设备信息列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM devices 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?
                ''', (limit, offset))
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"查询所有设备失败: {str(e)}")
            return []
    
    def add_scan_history(self, scan_info: Dict) -> bool:
        """
        添加扫描历史记录
        
        Args:
            scan_info: 扫描信息字典
            
        Returns:
            操作是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO scan_history (
                        sn_code, scan_result, image_path, operator
                    ) VALUES (?, ?, ?, ?)
                ''', (
                    scan_info.get('sn_code'),
                    scan_info.get('scan_result', ''),
                    scan_info.get('image_path', ''),
                    scan_info.get('operator', '')
                ))
                
                conn.commit()
                logger.info(f"扫描历史添加成功: {scan_info.get('sn_code')}")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"添加扫描历史失败: {str(e)}")
            return False
    
    def get_scan_history(self, sn_code: str = None, limit: int = 50) -> List[Dict]:
        """
        获取扫描历史记录
        
        Args:
            sn_code: 可选的SN码过滤
            limit: 限制返回数量
            
        Returns:
            扫描历史列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                if sn_code:
                    cursor.execute('''
                        SELECT * FROM scan_history 
                        WHERE sn_code = ?
                        ORDER BY scan_time DESC 
                        LIMIT ?
                    ''', (sn_code, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM scan_history 
                        ORDER BY scan_time DESC 
                        LIMIT ?
                    ''', (limit,))
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"查询扫描历史失败: {str(e)}")
            return []
    
    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 设备总数
                cursor.execute('SELECT COUNT(*) FROM devices')
                total_devices = cursor.fetchone()[0]
                
                # 今日扫描次数
                cursor.execute('''
                    SELECT COUNT(*) FROM scan_history 
                    WHERE DATE(scan_time) = DATE('now')
                ''')
                today_scans = cursor.fetchone()[0]
                
                # 活跃设备数
                cursor.execute("SELECT COUNT(*) FROM devices WHERE status = 'active'")
                active_devices = cursor.fetchone()[0]
                
                return {
                    'total_devices': total_devices,
                    'today_scans': today_scans,
                    'active_devices': active_devices
                }
                
        except sqlite3.Error as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}


# 测试函数
def test_database():
    """测试数据库功能"""
    db = DeviceDatabase("test_devices.db")
    
    # 测试添加设备
    test_device = {
        'sn_code': 'TEST001',
        'device_name': '测试设备',
        'device_type': '服务器',
        'manufacturer': '测试厂商',
        'model': 'TEST-MODEL-001'
    }
    
    print("测试添加设备...")
    result = db.add_device(test_device)
    print(f"添加结果: {result}")
    
    # 测试查询设备
    print("测试查询设备...")
    device = db.get_device('TEST001')
    print(f"查询结果: {device}")
    
    # 清理测试数据
    db.delete_device('TEST001')
    os.remove("test_devices.db")
    print("测试完成")


if __name__ == "__main__":
    test_database()
