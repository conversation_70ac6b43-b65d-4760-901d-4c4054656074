"""
GUI界面模块
使用tkinter创建用户友好的图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import threading
import logging
from typing import Optional, List, Dict
import os

from .barcode_scanner import BarcodeScanner
from .database import DeviceDatabase
from .image_processor import ImageProcessor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BarcodeGUI:
    """条形码识别GUI主界面类"""
    
    def __init__(self):
        """初始化GUI界面"""
        self.root = tk.Tk()
        self.root.title("条形码识别入库管理系统 v1.0")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 初始化组件
        self.scanner = BarcodeScanner()
        self.database = DeviceDatabase()
        self.processor = ImageProcessor()
        
        # 界面变量
        self.current_image = None
        self.current_results = []
        self.camera_running = False
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        
        logger.info("GUI界面初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 左侧面板 - 图像显示和控制
        self.left_panel = ttk.LabelFrame(self.main_frame, text="图像处理", padding=10)
        
        # 图像显示区域
        self.image_label = tk.Label(self.left_panel, text="请选择图片或启动摄像头", 
                                   bg="lightgray", width=50, height=20)
        
        # 控制按钮框架
        self.control_frame = ttk.Frame(self.left_panel)
        
        # 按钮
        self.btn_select_image = ttk.Button(self.control_frame, text="选择图片", 
                                          command=self._select_image)
        self.btn_camera = ttk.Button(self.control_frame, text="启动摄像头", 
                                    command=self._toggle_camera)
        self.btn_scan = ttk.Button(self.control_frame, text="扫描条形码", 
                                  command=self._scan_barcode)
        self.btn_enhance = ttk.Button(self.control_frame, text="图像增强", 
                                     command=self._enhance_image)
        
        # 右侧面板 - 结果显示和数据管理
        self.right_panel = ttk.LabelFrame(self.main_frame, text="识别结果与数据管理", padding=10)
        
        # 结果显示区域
        self.result_frame = ttk.LabelFrame(self.right_panel, text="扫描结果", padding=5)
        
        # 结果文本框
        self.result_text = tk.Text(self.result_frame, height=8, width=40)
        self.result_scrollbar = ttk.Scrollbar(self.result_frame, orient="vertical", 
                                             command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=self.result_scrollbar.set)
        
        # 设备信息输入框架
        self.device_frame = ttk.LabelFrame(self.right_panel, text="设备信息", padding=5)
        
        # 设备信息输入字段
        self.device_fields = {}
        fields = [
            ("SN码", "sn_code"),
            ("设备名称", "device_name"),
            ("设备类型", "device_type"),
            ("制造商", "manufacturer"),
            ("型号", "model"),
            ("位置", "location"),
            ("备注", "notes")
        ]
        
        for i, (label, field) in enumerate(fields):
            ttk.Label(self.device_frame, text=f"{label}:").grid(row=i, column=0, sticky="w", pady=2)
            entry = ttk.Entry(self.device_frame, width=25)
            entry.grid(row=i, column=1, sticky="ew", pady=2, padx=(5, 0))
            self.device_fields[field] = entry
        
        # 设备操作按钮
        self.device_btn_frame = ttk.Frame(self.device_frame)
        self.btn_add_device = ttk.Button(self.device_btn_frame, text="添加设备", 
                                        command=self._add_device)
        self.btn_update_device = ttk.Button(self.device_btn_frame, text="更新设备", 
                                           command=self._update_device)
        self.btn_delete_device = ttk.Button(self.device_btn_frame, text="删除设备", 
                                           command=self._delete_device)
        self.btn_clear_fields = ttk.Button(self.device_btn_frame, text="清空字段", 
                                          command=self._clear_fields)
        
        # 设备列表框架
        self.list_frame = ttk.LabelFrame(self.right_panel, text="设备列表", padding=5)
        
        # 设备列表
        columns = ("SN码", "设备名称", "类型", "制造商", "扫描时间")
        self.device_tree = ttk.Treeview(self.list_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.device_tree.heading(col, text=col)
            self.device_tree.column(col, width=100)
        
        self.tree_scrollbar = ttk.Scrollbar(self.list_frame, orient="vertical", 
                                           command=self.device_tree.yview)
        self.device_tree.configure(yscrollcommand=self.tree_scrollbar.set)
        
        # 绑定双击事件
        self.device_tree.bind("<Double-1>", self._on_device_select)
        
        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(self.status_frame, text="就绪")
        
        # 刷新按钮
        self.btn_refresh = ttk.Button(self.list_frame, text="刷新列表", 
                                     command=self._refresh_device_list)
    
    def _setup_layout(self):
        """设置界面布局"""
        # 主框架
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 左右面板
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 5))
        self.right_panel.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        # 左侧面板布局
        self.image_label.pack(pady=10)
        self.control_frame.pack(fill="x", pady=10)
        
        # 控制按钮布局
        self.btn_select_image.pack(side="left", padx=5)
        self.btn_camera.pack(side="left", padx=5)
        self.btn_scan.pack(side="left", padx=5)
        self.btn_enhance.pack(side="left", padx=5)
        
        # 右侧面板布局
        self.result_frame.pack(fill="x", pady=(0, 10))
        self.device_frame.pack(fill="x", pady=(0, 10))
        self.list_frame.pack(fill="both", expand=True)
        
        # 结果显示区域布局
        self.result_text.pack(side="left", fill="both", expand=True)
        self.result_scrollbar.pack(side="right", fill="y")
        
        # 设备信息框架布局
        self.device_frame.columnconfigure(1, weight=1)
        self.device_btn_frame.grid(row=len(self.device_fields), column=0, columnspan=2, 
                                  pady=10, sticky="ew")
        
        # 设备操作按钮布局
        self.btn_add_device.pack(side="left", padx=2)
        self.btn_update_device.pack(side="left", padx=2)
        self.btn_delete_device.pack(side="left", padx=2)
        self.btn_clear_fields.pack(side="left", padx=2)
        
        # 设备列表布局
        self.device_tree.pack(side="left", fill="both", expand=True)
        self.tree_scrollbar.pack(side="right", fill="y")
        self.btn_refresh.pack(pady=5)
        
        # 状态栏
        self.status_frame.pack(fill="x", pady=(10, 0))
        self.status_label.pack(side="left")
        
        # 初始化设备列表
        self._refresh_device_list()
    
    def _select_image(self):
        """选择图片文件"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self._load_image(file_path)
            self._update_status(f"已加载图片: {os.path.basename(file_path)}")
    
    def _load_image(self, image_path: str):
        """加载并显示图片"""
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                messagebox.showerror("错误", f"图片文件不存在: {image_path}")
                return

            # 检查文件大小
            if os.path.getsize(image_path) == 0:
                logger.error(f"图片文件为空: {image_path}")
                messagebox.showerror("错误", f"图片文件为空: {image_path}")
                return

            # 使用OpenCV读取图片
            self.current_image = cv2.imread(image_path)

            # 检查图片是否成功读取
            if self.current_image is None:
                logger.error(f"无法读取图片文件: {image_path}")
                messagebox.showerror("错误", f"无法读取图片文件，可能是格式不支持或文件损坏")
                return

            # 检查图片是否为空
            if self.current_image.size == 0:
                logger.error(f"读取的图片为空: {image_path}")
                messagebox.showerror("错误", f"读取的图片为空")
                return

            # 转换为PIL格式并调整大小显示
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)

            # 调整图片大小以适应显示区域
            display_size = (400, 300)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

            # 转换为tkinter可显示的格式
            photo = ImageTk.PhotoImage(pil_image)

            # 更新显示
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用

            logger.info(f"图片加载成功: {os.path.basename(image_path)}")

        except Exception as e:
            logger.error(f"加载图片失败: {str(e)}")
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")
    
    def _toggle_camera(self):
        """切换摄像头状态"""
        if not self.camera_running:
            self._start_camera()
        else:
            self._stop_camera()
    
    def _start_camera(self):
        """启动摄像头"""
        try:
            self.camera_running = True
            self.btn_camera.configure(text="停止摄像头")
            
            # 在新线程中运行摄像头
            camera_thread = threading.Thread(target=self._camera_loop)
            camera_thread.daemon = True
            camera_thread.start()
            
            self._update_status("摄像头已启动")
            
        except Exception as e:
            logger.error(f"启动摄像头失败: {str(e)}")
            messagebox.showerror("错误", f"启动摄像头失败: {str(e)}")
            self.camera_running = False
    
    def _stop_camera(self):
        """停止摄像头"""
        self.camera_running = False
        self.btn_camera.configure(text="启动摄像头")
        self._update_status("摄像头已停止")
    
    def _camera_loop(self):
        """摄像头循环"""
        cap = cv2.VideoCapture(0)
        
        while self.camera_running:
            ret, frame = cap.read()
            if ret:
                self.current_image = frame.copy()
                
                # 转换并显示
                image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(image_rgb)
                
                display_size = (400, 300)
                pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)
                
                photo = ImageTk.PhotoImage(pil_image)
                
                # 在主线程中更新UI
                self.root.after(0, self._update_camera_display, photo)
        
        cap.release()
    
    def _update_camera_display(self, photo):
        """更新摄像头显示"""
        self.image_label.configure(image=photo, text="")
        self.image_label.image = photo
    
    def _scan_barcode(self):
        """扫描条形码"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择图片或启动摄像头")
            return

        # 检查当前图像是否有效
        if self.current_image.size == 0:
            messagebox.showerror("错误", "当前图像为空，请重新加载图片")
            return

        try:
            self._update_status("正在扫描条形码...")

            # 扫描条形码
            results = self.scanner._decode_barcodes(self.current_image)
            self.current_results = results

            # 显示结果
            self._display_scan_results(results)

            if results:
                # 自动填充第一个结果到SN码字段
                first_result = results[0]
                self.device_fields['sn_code'].delete(0, tk.END)
                self.device_fields['sn_code'].insert(0, first_result['data'])

                self._update_status(f"扫描完成，识别到 {len(results)} 个条形码")

                # 如果直接识别失败，提示用户尝试图像增强
                if not results:
                    messagebox.showinfo("提示", "未识别到条形码，建议点击'图像增强'按钮后再试")
            else:
                self._update_status("未识别到条形码，建议尝试图像增强")
                messagebox.showinfo("提示", "未识别到条形码，建议点击'图像增强'按钮后再试")

        except Exception as e:
            logger.error(f"扫描条形码失败: {str(e)}")
            messagebox.showerror("错误", f"扫描条形码失败: {str(e)}")
            self._update_status("扫描失败")
    
    def _enhance_image(self):
        """增强图像"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择图片或启动摄像头")
            return

        # 检查当前图像是否有效
        if self.current_image.size == 0:
            messagebox.showerror("错误", "当前图像为空，请重新加载图片")
            return

        try:
            self._update_status("正在增强图像...")

            # 增强图像
            enhanced = self.processor.auto_enhance_for_barcode(self.current_image)

            # 检查增强结果
            if enhanced is None or enhanced.size == 0:
                messagebox.showerror("错误", "图像增强失败，增强后图像为空")
                self._update_status("图像增强失败")
                return

            # 显示增强后的图像
            if len(enhanced.shape) == 2:  # 灰度图像
                enhanced_rgb = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2RGB)
            else:
                enhanced_rgb = cv2.cvtColor(enhanced, cv2.COLOR_BGR2RGB)

            pil_image = Image.fromarray(enhanced_rgb)
            display_size = (400, 300)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(pil_image)
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo

            # 更新当前图像为增强后的图像
            if len(enhanced.shape) == 2:
                self.current_image = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
            else:
                self.current_image = enhanced

            self._update_status("图像增强完成，现在可以尝试扫描")
            messagebox.showinfo("提示", "图像增强完成！现在可以点击'扫描条形码'按钮")

        except Exception as e:
            logger.error(f"图像增强失败: {str(e)}")
            messagebox.showerror("错误", f"图像增强失败: {str(e)}")
            self._update_status("图像增强失败")
    
    def _display_scan_results(self, results: List[Dict]):
        """显示扫描结果"""
        self.result_text.delete(1.0, tk.END)
        
        if not results:
            self.result_text.insert(tk.END, "未识别到条形码\n")
            return
        
        for i, result in enumerate(results, 1):
            self.result_text.insert(tk.END, f"条形码 {i}:\n")
            self.result_text.insert(tk.END, f"  数据: {result['data']}\n")
            self.result_text.insert(tk.END, f"  类型: {result['type']}\n")
            self.result_text.insert(tk.END, f"  位置: {result['position']}\n")
            self.result_text.insert(tk.END, "\n")
    
    def _add_device(self):
        """添加设备"""
        device_info = self._get_device_info()
        
        if not device_info.get('sn_code'):
            messagebox.showwarning("警告", "请输入SN码")
            return
        
        try:
            success = self.database.add_device(device_info)
            
            if success:
                messagebox.showinfo("成功", "设备添加成功")
                self._clear_fields()
                self._refresh_device_list()
                self._update_status("设备添加成功")
            else:
                messagebox.showerror("错误", "设备添加失败，SN码可能已存在")
                
        except Exception as e:
            logger.error(f"添加设备失败: {str(e)}")
            messagebox.showerror("错误", f"添加设备失败: {str(e)}")
    
    def _update_device(self):
        """更新设备"""
        sn_code = self.device_fields['sn_code'].get().strip()
        
        if not sn_code:
            messagebox.showwarning("警告", "请输入要更新的设备SN码")
            return
        
        device_info = self._get_device_info()
        # 移除SN码，因为它是主键
        device_info.pop('sn_code', None)
        
        try:
            success = self.database.update_device(sn_code, device_info)
            
            if success:
                messagebox.showinfo("成功", "设备更新成功")
                self._refresh_device_list()
                self._update_status("设备更新成功")
            else:
                messagebox.showerror("错误", "设备更新失败，未找到指定设备")
                
        except Exception as e:
            logger.error(f"更新设备失败: {str(e)}")
            messagebox.showerror("错误", f"更新设备失败: {str(e)}")
    
    def _delete_device(self):
        """删除设备"""
        sn_code = self.device_fields['sn_code'].get().strip()
        
        if not sn_code:
            messagebox.showwarning("警告", "请输入要删除的设备SN码")
            return
        
        # 确认删除
        if not messagebox.askyesno("确认", f"确定要删除设备 {sn_code} 吗？"):
            return
        
        try:
            success = self.database.delete_device(sn_code)
            
            if success:
                messagebox.showinfo("成功", "设备删除成功")
                self._clear_fields()
                self._refresh_device_list()
                self._update_status("设备删除成功")
            else:
                messagebox.showerror("错误", "设备删除失败，未找到指定设备")
                
        except Exception as e:
            logger.error(f"删除设备失败: {str(e)}")
            messagebox.showerror("错误", f"删除设备失败: {str(e)}")
    
    def _get_device_info(self) -> Dict:
        """获取设备信息"""
        device_info = {}
        for field, entry in self.device_fields.items():
            device_info[field] = entry.get().strip()
        return device_info
    
    def _clear_fields(self):
        """清空输入字段"""
        for entry in self.device_fields.values():
            entry.delete(0, tk.END)
    
    def _refresh_device_list(self):
        """刷新设备列表"""
        try:
            # 清空现有数据
            for item in self.device_tree.get_children():
                self.device_tree.delete(item)
            
            # 获取设备列表
            devices = self.database.get_all_devices()
            
            # 添加到树形控件
            for device in devices:
                self.device_tree.insert("", "end", values=(
                    device.get('sn_code', ''),
                    device.get('device_name', ''),
                    device.get('device_type', ''),
                    device.get('manufacturer', ''),
                    device.get('scan_time', '')
                ))
            
            self._update_status(f"设备列表已刷新，共 {len(devices)} 台设备")
            
        except Exception as e:
            logger.error(f"刷新设备列表失败: {str(e)}")
            messagebox.showerror("错误", f"刷新设备列表失败: {str(e)}")
    
    def _on_device_select(self, event):
        """设备选择事件"""
        selection = self.device_tree.selection()
        if selection:
            item = self.device_tree.item(selection[0])
            sn_code = item['values'][0]
            
            # 获取设备详细信息
            device = self.database.get_device(sn_code)
            if device:
                # 填充到输入字段
                for field, entry in self.device_fields.items():
                    entry.delete(0, tk.END)
                    entry.insert(0, device.get(field, ''))
    
    def _update_status(self, message: str):
        """更新状态栏"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()
    
    def run(self):
        """运行GUI应用"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        except Exception as e:
            logger.error(f"GUI运行错误: {str(e)}")
        finally:
            if self.camera_running:
                self._stop_camera()


# 测试函数
def test_gui():
    """测试GUI界面"""
    app = BarcodeGUI()
    app.run()


if __name__ == "__main__":
    test_gui()
